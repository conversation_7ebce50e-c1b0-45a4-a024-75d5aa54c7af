import { pgTable, index, foreignKey, text, integer, timestamp, unique, varchar, boolean, primaryKey } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const creditTransaction = pgTable("credit_transaction", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	type: text().notNull(),
	description: text(),
	amount: integer().notNull(),
	remainingAmount: integer("remaining_amount"),
	paymentId: text("payment_id"),
	expirationDate: timestamp("expiration_date", { mode: 'string' }),
	expirationDateProcessedAt: timestamp("expiration_date_processed_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("credit_transaction_type_idx").using("btree", table.type.asc().nullsLast().op("text_ops")),
	index("credit_transaction_user_id_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "credit_transaction_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const items = pgTable("items", {
	id: text().primaryKey().notNull(),
	name: varchar({ length: 100 }).notNull(),
	slug: varchar({ length: 120 }).notNull(),
	description: text().notNull(),
	introduction: text(),
	link: varchar({ length: 500 }).notNull(),
	imageUrl: varchar("image_url", { length: 500 }),
	publishDate: timestamp("publish_date", { mode: 'string' }),
	featured: boolean().default(false),
	paid: boolean().default(false),
	pricePlan: varchar("price_plan", { length: 20 }).default('FREE'),
	freePlanStatus: varchar("free_plan_status", { length: 20 }).default('published'),
	proPlanStatus: varchar("pro_plan_status", { length: 20 }),
	rejectionReason: text("rejection_reason"),
	creditsUsed: integer("credits_used").default(0),
	submitterId: text("submitter_id").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("item_created_at_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("item_featured_idx").using("btree", table.featured.asc().nullsLast().op("bool_ops")),
	index("item_paid_idx").using("btree", table.paid.asc().nullsLast().op("bool_ops")),
	index("item_publish_date_idx").using("btree", table.publishDate.asc().nullsLast().op("timestamp_ops")),
	index("item_slug_idx").using("btree", table.slug.asc().nullsLast().op("text_ops")),
	index("item_submitter_idx").using("btree", table.submitterId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.submitterId],
			foreignColumns: [user.id],
			name: "items_submitter_id_user_id_fk"
		}),
	unique("items_slug_unique").on(table.slug),
]);

export const categories = pgTable("categories", {
	id: text().primaryKey().notNull(),
	name: varchar({ length: 50 }).notNull(),
	slug: varchar({ length: 60 }).notNull(),
	description: text(),
	priority: integer().default(0),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("category_priority_idx").using("btree", table.priority.asc().nullsLast().op("int4_ops")),
	index("category_slug_idx").using("btree", table.slug.asc().nullsLast().op("text_ops")),
	unique("categories_slug_unique").on(table.slug),
]);

export const tags = pgTable("tags", {
	id: text().primaryKey().notNull(),
	name: varchar({ length: 30 }).notNull(),
	slug: varchar({ length: 40 }).notNull(),
	description: text(),
	color: varchar({ length: 7 }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("tag_name_idx").using("btree", table.name.asc().nullsLast().op("text_ops")),
	index("tag_slug_idx").using("btree", table.slug.asc().nullsLast().op("text_ops")),
	unique("tags_slug_unique").on(table.slug),
]);

export const payment = pgTable("payment", {
	id: text().primaryKey().notNull(),
	priceId: text("price_id").notNull(),
	type: text().notNull(),
	interval: text(),
	userId: text("user_id").notNull(),
	customerId: text("customer_id").notNull(),
	subscriptionId: text("subscription_id"),
	sessionId: text("session_id"),
	status: text().notNull(),
	periodStart: timestamp("period_start", { mode: 'string' }),
	periodEnd: timestamp("period_end", { mode: 'string' }),
	cancelAtPeriodEnd: boolean("cancel_at_period_end"),
	trialStart: timestamp("trial_start", { mode: 'string' }),
	trialEnd: timestamp("trial_end", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("payment_customer_id_idx").using("btree", table.customerId.asc().nullsLast().op("text_ops")),
	index("payment_price_id_idx").using("btree", table.priceId.asc().nullsLast().op("text_ops")),
	index("payment_session_id_idx").using("btree", table.sessionId.asc().nullsLast().op("text_ops")),
	index("payment_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("payment_subscription_id_idx").using("btree", table.subscriptionId.asc().nullsLast().op("text_ops")),
	index("payment_type_idx").using("btree", table.type.asc().nullsLast().op("text_ops")),
	index("payment_user_id_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "payment_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const session = pgTable("session", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	token: text().notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id").notNull(),
	impersonatedBy: text("impersonated_by"),
}, (table) => [
	index("session_token_idx").using("btree", table.token.asc().nullsLast().op("text_ops")),
	index("session_user_id_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_user_id_user_id_fk"
		}).onDelete("cascade"),
	unique("session_token_unique").on(table.token),
]);

export const account = pgTable("account", {
	id: text().primaryKey().notNull(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id").notNull(),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at", { mode: 'string' }),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at", { mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).notNull(),
}, (table) => [
	index("account_account_id_idx").using("btree", table.accountId.asc().nullsLast().op("text_ops")),
	index("account_provider_id_idx").using("btree", table.providerId.asc().nullsLast().op("text_ops")),
	index("account_user_id_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }),
	updatedAt: timestamp("updated_at", { mode: 'string' }),
});

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	emailVerified: boolean("email_verified").notNull(),
	image: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).notNull(),
	role: text(),
	banned: boolean(),
	banReason: text("ban_reason"),
	banExpires: timestamp("ban_expires", { mode: 'string' }),
	customerId: text("customer_id"),
}, (table) => [
	index("user_customer_id_idx").using("btree", table.customerId.asc().nullsLast().op("text_ops")),
	index("user_id_idx").using("btree", table.id.asc().nullsLast().op("text_ops")),
	index("user_role_idx").using("btree", table.role.asc().nullsLast().op("text_ops")),
	unique("user_email_unique").on(table.email),
]);

export const userCredit = pgTable("user_credit", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	currentCredits: integer("current_credits").default(0).notNull(),
	lastRefreshAt: timestamp("last_refresh_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("user_credit_user_id_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_credit_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const itemTags = pgTable("item_tags", {
	itemId: text("item_id").notNull(),
	tagId: text("tag_id").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("item_tags_item_idx").using("btree", table.itemId.asc().nullsLast().op("text_ops")),
	index("item_tags_tag_idx").using("btree", table.tagId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.itemId],
			foreignColumns: [items.id],
			name: "item_tags_item_id_items_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.tagId],
			foreignColumns: [tags.id],
			name: "item_tags_tag_id_tags_id_fk"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.itemId, table.tagId], name: "item_tags_item_id_tag_id_pk"}),
]);

export const itemCategories = pgTable("item_categories", {
	itemId: text("item_id").notNull(),
	categoryId: text("category_id").notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	index("item_categories_category_idx").using("btree", table.categoryId.asc().nullsLast().op("text_ops")),
	index("item_categories_item_idx").using("btree", table.itemId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.itemId],
			foreignColumns: [items.id],
			name: "item_categories_item_id_items_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.categoryId],
			foreignColumns: [categories.id],
			name: "item_categories_category_id_categories_id_fk"
		}).onDelete("cascade"),
	primaryKey({ columns: [table.itemId, table.categoryId], name: "item_categories_item_id_category_id_pk"}),
]);
