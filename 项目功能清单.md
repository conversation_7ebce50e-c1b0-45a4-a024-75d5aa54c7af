# Mkdirs 产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位
Mkdirs 是一个现代化的目录网站模板，专为快速构建盈利性目录平台而设计。产品面向需要建立产品目录、服务目录、工具推荐等聚合平台的创业者和企业。

### 1.2 产品愿景
成为最易用、功能最完整的目录网站解决方案，让任何人都能在几分钟内搭建专业的目录平台。

### 1.3 目标用户
- **主要用户**: 独立开发者、创业者、小型企业
- **次要用户**: 数字营销机构、内容创作者
- **用户画像**:
  - 技术背景：具备基础的网站部署能力
  - 业务需求：需要快速上线目录类网站
  - 预算范围：中小型项目预算

### 1.4 核心价值主张
- **快速部署**: 5分钟内完成基础配置
- **功能完整**: 涵盖目录网站所需的全部功能
- **商业化就绪**: 内置支付和变现功能
- **SEO 优化**: 开箱即用的搜索引擎优化
- **可扩展性**: 模块化架构支持自定义扩展

### 1.5 技术架构
- **前端**: Next.js 14 (App Router), TypeScript, Tailwind CSS
- **后端**: Next.js API Routes, Server Actions
- **数据库**: Sanity CMS (Headless CMS)
- **支付**: Stripe 集成
- **认证**: Auth.js (NextAuth)
- **邮件**: Resend
- **部署**: Vercel, Docker 支持

---

## 2. 功能需求

### 2.1 用户认证与权限管理

#### 2.1.1 用户注册登录
**功能描述**: 提供安全可靠的用户身份验证系统
**业务价值**: 建立用户体系，支持个性化服务和付费功能

**技术实现详解**:

##### 用户注册流程
```typescript
// 注册表单验证Schema
const RegisterSchema = z.object({
  name: z.string()
    .min(2, "姓名至少2个字符")
    .max(50, "姓名不能超过50个字符"),
  email: z.string()
    .email("请输入有效的邮箱地址")
    .refine(async (email) => {
      // 检查邮箱是否已存在
      const existingUser = await getUserByEmail(email);
      return !existingUser;
    }, "该邮箱已被注册"),
  password: z.string()
    .min(8, "密码至少8位")
    .regex(/^(?=.*[a-zA-Z])(?=.*\d)/, "密码必须包含字母和数字"),
  confirmPassword: z.string()
}).refine((data) => data.password === data.confirmPassword, {
  message: "两次密码输入不一致",
  path: ["confirmPassword"],
});

// 注册处理逻辑
export async function registerAction(formData: RegisterFormData) {
  try {
    // 1. 表单验证
    const validatedInput = RegisterSchema.safeParse(formData);
    if (!validatedInput.success) {
      return {
        status: "error",
        message: "表单验证失败",
        errors: validatedInput.error.flatten().fieldErrors
      };
    }

    const { name, email, password } = validatedInput.data;

    // 2. 密码加密
    const hashedPassword = await hashPassword(password);

    // 3. 创建用户记录
    const user = await sanityClient.create({
      _type: "user",
      name,
      email,
      password: hashedPassword,
      role: "USER",
      emailVerified: null, // 待验证
      image: null,
      link: null,
    });

    // 4. 生成验证令牌
    const verificationToken = await generateVerificationToken(email);

    // 5. 发送验证邮件
    await sendVerificationEmail(email, verificationToken.token);

    return {
      status: "success",
      message: "注册成功！请检查邮箱并点击验证链接激活账户。"
    };

  } catch (error) {
    console.error("注册失败:", error);
    return { status: "error", message: "注册失败，请重试" };
  }
}

// 密码加密函数
async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// 密码验证函数
async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}
```

##### 邮箱验证流程
```typescript
// 生成验证令牌
export async function generateVerificationToken(email: string) {
  const token = `token.${uuid()}`;
  const expires = new Date(Date.now() + 3600 * 1000); // 1小时后过期

  // 删除已存在的令牌
  const existingToken = await getVerificationTokenByEmail(email);
  if (existingToken) {
    await sanityClient.delete(existingToken._id);
  }

  // 创建新令牌
  const verificationToken = await sanityClient.create({
    _type: "verificationToken",
    identifier: email,
    token,
    expires: expires.toISOString(),
  });

  return verificationToken;
}

// 验证邮箱处理
export async function verifyEmailAction(token: string) {
  try {
    // 1. 查找令牌
    const verificationToken = await getVerificationTokenByToken(token);
    if (!verificationToken) {
      return { status: "error", message: "验证链接无效" };
    }

    // 2. 检查令牌是否过期
    if (new Date() > new Date(verificationToken.expires)) {
      await sanityClient.delete(verificationToken._id);
      return { status: "error", message: "验证链接已过期" };
    }

    // 3. 查找用户
    const user = await getUserByEmail(verificationToken.identifier);
    if (!user) {
      return { status: "error", message: "用户不存在" };
    }

    // 4. 更新用户验证状态
    await sanityClient
      .patch(user._id)
      .set({ emailVerified: new Date().toISOString() })
      .commit();

    // 5. 删除验证令牌
    await sanityClient.delete(verificationToken._id);

    return { status: "success", message: "邮箱验证成功！" };

  } catch (error) {
    console.error("邮箱验证失败:", error);
    return { status: "error", message: "验证失败，请重试" };
  }
}
```

##### 登录认证流程
```typescript
// NextAuth配置
export const authConfig = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // 1. 查找用户
        const user = await getUserByEmail(credentials.email);
        if (!user || !user.password) {
          return null;
        }

        // 2. 验证密码
        const passwordsMatch = await verifyPassword(
          credentials.password,
          user.password
        );

        if (!passwordsMatch) {
          return null;
        }

        // 3. 检查邮箱验证状态
        if (!user.emailVerified) {
          throw new Error("请先验证邮箱");
        }

        // 4. 返回用户信息
        return {
          id: user._id,
          name: user.name,
          email: user.email,
          role: user.role,
          image: user.image,
        };
      },
    }),
  ],
  callbacks: {
    // JWT回调
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
      }
      return token;
    },

    // Session回调
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
      }
      return session;
    },

    // 登录回调
    async signIn({ user, account }) {
      if (account?.provider !== "credentials") return true;

      const existingUser = await getUserById(user.id!);

      // 防止未验证邮箱的用户登录
      if (!existingUser?.emailVerified) {
        return false;
      }

      return true;
    },
  },
  pages: {
    signIn: "/auth/login",
    error: "/auth/error",
  },
  session: { strategy: "jwt" },
  adapter: SanityAdapter(sanityClient),
};
```

##### 密码重置流程
```typescript
// 发起密码重置
export async function resetPasswordAction(email: string) {
  try {
    // 1. 检查用户是否存在
    const user = await getUserByEmail(email);
    if (!user) {
      // 为了安全，不透露用户是否存在
      return { status: "success", message: "如果邮箱存在，重置链接已发送" };
    }

    // 2. 生成重置令牌
    const resetToken = await generatePasswordResetToken(email);

    // 3. 发送重置邮件
    await sendPasswordResetEmail(user.name, email, resetToken.token);

    return { status: "success", message: "重置链接已发送到您的邮箱" };

  } catch (error) {
    console.error("密码重置失败:", error);
    return { status: "error", message: "操作失败，请重试" };
  }
}

// 执行密码重置
export async function newPasswordAction(token: string, password: string) {
  try {
    // 1. 验证令牌
    const resetToken = await getPasswordResetTokenByToken(token);
    if (!resetToken) {
      return { status: "error", message: "重置链接无效" };
    }

    if (new Date() > new Date(resetToken.expires)) {
      await sanityClient.delete(resetToken._id);
      return { status: "error", message: "重置链接已过期" };
    }

    // 2. 查找用户
    const user = await getUserByEmail(resetToken.identifier);
    if (!user) {
      return { status: "error", message: "用户不存在" };
    }

    // 3. 更新密码
    const hashedPassword = await hashPassword(password);
    await sanityClient
      .patch(user._id)
      .set({ password: hashedPassword })
      .commit();

    // 4. 删除重置令牌
    await sanityClient.delete(resetToken._id);

    return { status: "success", message: "密码重置成功！" };

  } catch (error) {
    console.error("密码重置失败:", error);
    return { status: "error", message: "重置失败，请重试" };
  }
}
```

**验收标准**:
- 注册成功率 > 95%
- 邮件发送成功率 > 98%
- 登录响应时间 < 2秒
- 密码加密强度: bcrypt saltRounds >= 12
- 令牌过期时间: 1小时
- 登录失败锁定: 5次失败后锁定15分钟

#### 2.1.2 权限控制
**功能描述**: 基于角色的访问控制系统
**业务价值**: 保护敏感功能，支持多层级管理

**详细需求**:
- **角色定义**:
  - USER: 普通用户，可提交和管理自己的项目
  - ADMIN: 管理员，可审核所有项目和管理用户
- **权限矩阵**:
  - 页面访问权限（公开/登录/管理员）
  - 功能操作权限（查看/编辑/删除）
  - API 接口权限控制
- **中间件保护**:
  - 路由级别访问控制
  - 自动重定向到登录页
  - 会话过期处理

### 2.2 目录管理核心功能

#### 2.2.1 项目提交与管理
**功能描述**: 用户提交产品/服务到目录的完整流程
**业务价值**: 平台内容的主要来源，支持UGC模式

**技术流程详解**:

##### 步骤1: 用户身份验证
```typescript
// 1. 检查用户登录状态
const user = await currentUser();
if (!user) {
  redirect('/auth/login?callbackUrl=/submit');
}

// 2. 验证用户权限
if (user.role !== 'USER' && user.role !== 'ADMIN') {
  throw new Error('Unauthorized');
}

// 3. 检查用户邮箱验证状态
if (!user.emailVerified) {
  redirect('/auth/verify-email');
}
```

##### 步骤2: 表单数据验证
```typescript
// 前端实时验证
const SubmitSchema = z.object({
  name: z.string()
    .min(1, "项目名称不能为空")
    .max(100, "项目名称不能超过100字符"),
  link: z.string()
    .url("请输入有效的URL")
    .refine(async (url) => {
      // 检查URL可访问性
      try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
      } catch {
        return false;
      }
    }, "URL无法访问"),
  description: z.string()
    .min(10, "描述至少10个字符")
    .max(500, "描述不能超过500字符"),
  categories: z.array(z.string()).min(1, "请选择至少一个分类"),
  tags: z.array(z.string()).max(5, "最多选择5个标签"),
  imageId: z.string().min(1, "请上传项目截图")
});

// 后端验证
export async function submitAction(formData: SubmitFormData) {
  const validatedInput = SubmitSchema.safeParse(formData);
  if (!validatedInput.success) {
    return { status: "error", message: "表单验证失败" };
  }
}
```

##### 步骤3: 图片上传处理
```typescript
// 图片上传API: /api/upload-image
export async function POST(request: NextRequest) {
  try {
    // 1. 获取上传文件
    const formData = await request.formData();
    const file = formData.get("file") as File;

    // 2. 文件格式验证
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: "不支持的文件格式" }, { status: 400 });
    }

    // 3. 文件大小验证
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: "文件大小超过限制" }, { status: 400 });
    }

    // 4. 上传到Sanity
    const asset = await sanityClient.assets.upload("image", file, {
      filename: file.name,
    });

    // 5. 返回资源ID
    return NextResponse.json({
      asset: {
        _id: asset._id,
        url: asset.url,
        metadata: asset.metadata
      }
    });
  } catch (error) {
    return NextResponse.json({ error: "上传失败" }, { status: 500 });
  }
}
```

##### 步骤4: 数据处理与存储
```typescript
// 提交处理逻辑
export async function submitAction(formData: SubmitFormData) {
  try {
    // 1. 生成唯一slug
    const baseSlug = slugify(formData.name);
    let slug = baseSlug;
    let counter = 1;

    // 检查slug唯一性
    while (await checkSlugExists(slug)) {
      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    // 2. 构建数据对象
    const itemData = {
      _type: "item",
      name: formData.name,
      slug: { _type: "slug", current: slug },
      link: formData.link,
      description: formData.description,
      introduction: formData.introduction || "",

      // 状态管理
      publishDate: null,
      paid: false,
      pricePlan: PricePlans.FREE,
      freePlanStatus: FreePlanStatus.SUBMITTING,

      // 关联数据
      submitter: {
        _type: "reference",
        _ref: user.id,
      },
      categories: formData.categories.map((categoryId, index) => ({
        _type: "reference",
        _ref: categoryId,
        _key: index.toString(),
      })),
      tags: formData.tags.map((tagId, index) => ({
        _type: "reference",
        _ref: tagId,
        _key: index.toString(),
      })),
      image: {
        _type: "image",
        alt: `${formData.name}的截图`,
        asset: {
          _type: "reference",
          _ref: formData.imageId,
        },
      },
    };

    // 3. 保存到数据库
    const result = await sanityClient.create(itemData);

    // 4. 状态更新为待审核
    await sanityClient
      .patch(result._id)
      .set({ freePlanStatus: FreePlanStatus.PENDING })
      .commit();

    return {
      status: "success",
      message: "项目提交成功",
      itemId: result._id
    };

  } catch (error) {
    console.error("提交失败:", error);
    return { status: "error", message: "提交失败，请重试" };
  }
}
```

##### 步骤5: 邮件通知触发
```typescript
// 提交成功后的通知流程
if (result.status === "success") {
  // 1. 生成状态查看链接
  const statusLink = `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`;
  const reviewLink = `${process.env.NEXT_PUBLIC_APP_URL}/studio/desk/item;${result.itemId}`;

  // 2. 发送用户确认邮件
  await sendNotifySubmissionEmail(
    user.name,
    user.email,
    formData.name,
    statusLink,
    reviewLink
  );

  // 3. 通知管理员审核
  await resend.emails.send({
    from: process.env.RESEND_EMAIL_FROM,
    to: process.env.RESEND_EMAIL_ADMIN,
    subject: "新项目待审核",
    react: NotifySubmissionEmail({
      itemName: formData.name,
      reviewLink
    }),
  });
}
```

**状态流转逻辑**:
```typescript
enum FreePlanStatus {
  SUBMITTING = "submitting",    // 提交中
  PENDING = "pending",          // 待审核
  APPROVED = "approved",        // 已通过
  REJECTED = "rejected",        // 已拒绝
}

enum ProPlanStatus {
  SUCCESS = "success",          // 支付成功
  FAILED = "failed",           // 支付失败
}

// 状态转换规则
const statusTransitions = {
  [FreePlanStatus.SUBMITTING]: [FreePlanStatus.PENDING],
  [FreePlanStatus.PENDING]: [FreePlanStatus.APPROVED, FreePlanStatus.REJECTED],
  [FreePlanStatus.APPROVED]: [FreePlanStatus.PENDING], // 重新编辑后
  [FreePlanStatus.REJECTED]: [FreePlanStatus.PENDING], // 重新提交后
};
```

**验收标准**:
- 提交成功率 > 99%
- 图片上传成功率 > 95%
- 表单验证准确率 100%
- 邮件发送成功率 > 98%
- 状态流转准确率 100%

#### 2.2.2 分类与标签系统
**功能描述**: 灵活的内容组织和筛选系统
**业务价值**: 提升用户查找效率，支持精准推荐

**技术实现详解**:

##### 分类管理系统
```typescript
// 分类数据服务
export class CategoryService {
  // 获取分类树结构
  static async getCategoryTree(): Promise<CategoryTree[]> {
    const categories = await sanityFetch<Category[]>({
      query: `*[_type == "category"] | order(priority desc, name asc) {
        _id,
        name,
        slug,
        description,
        priority,
        "itemCount": count(*[_type == "item" && references(^._id) &&
          (freePlanStatus == "approved" || proPlanStatus == "success")])
      }`,
    });

    // 构建树形结构（如果需要层级分类）
    return this.buildCategoryTree(categories);
  }

  // 构建分类树
  private static buildCategoryTree(categories: Category[]): CategoryTree[] {
    const categoryMap = new Map<string, CategoryTree>();
    const rootCategories: CategoryTree[] = [];

    // 创建分类节点
    categories.forEach(category => {
      categoryMap.set(category._id, {
        ...category,
        children: [],
      });
    });

    // 构建父子关系（如果有父分类字段）
    categories.forEach(category => {
      const node = categoryMap.get(category._id)!;
      if (category.parent) {
        const parent = categoryMap.get(category.parent._ref);
        if (parent) {
          parent.children.push(node);
        }
      } else {
        rootCategories.push(node);
      }
    });

    return rootCategories;
  }

  // 分类统计更新
  static async updateCategoryStats() {
    const categories = await sanityFetch<Category[]>({
      query: `*[_type == "category"] { _id }`,
    });

    for (const category of categories) {
      const itemCount = await sanityFetch<number>({
        query: `count(*[_type == "item" && references($categoryId) &&
          (freePlanStatus == "approved" || proPlanStatus == "success")])`,
        params: { categoryId: category._id },
      });

      // 更新统计数据（可以存储在单独的统计文档中）
      await sanityClient.createOrReplace({
        _id: `category-stats-${category._id}`,
        _type: 'categoryStats',
        category: { _type: 'reference', _ref: category._id },
        itemCount,
        lastUpdated: new Date().toISOString(),
      });
    }
  }
}

// 分类选择组件
export function CategorySelector({
  value,
  onChange,
  multiple = false
}: {
  value: string | string[];
  onChange: (value: string | string[]) => void;
  multiple?: boolean;
}) {
  const [categories, setCategories] = useState<CategoryTree[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    CategoryService.getCategoryTree().then(data => {
      setCategories(data);
      setIsLoading(false);
    });
  }, []);

  const renderCategory = (category: CategoryTree, level = 0) => (
    <div key={category._id} className={`pl-${level * 4}`}>
      <label className="flex items-center space-x-2">
        <input
          type={multiple ? "checkbox" : "radio"}
          name="category"
          value={category._id}
          checked={
            multiple
              ? (value as string[]).includes(category._id)
              : value === category._id
          }
          onChange={(e) => {
            if (multiple) {
              const currentValues = value as string[];
              if (e.target.checked) {
                onChange([...currentValues, category._id]);
              } else {
                onChange(currentValues.filter(id => id !== category._id));
              }
            } else {
              onChange(category._id);
            }
          }}
        />
        <span className="text-sm">
          {category.name} ({category.itemCount})
        </span>
      </label>
      {category.children.map(child => renderCategory(child, level + 1))}
    </div>
  );

  if (isLoading) return <div>加载中...</div>;

  return (
    <div className="space-y-2">
      {categories.map(category => renderCategory(category))}
    </div>
  );
}
```

##### 标签管理系统
```typescript
// 标签数据服务
export class TagService {
  // 获取热门标签
  static async getPopularTags(limit = 20): Promise<TagWithCount[]> {
    return await sanityFetch<TagWithCount[]>({
      query: `*[_type == "tag"] {
        _id,
        name,
        slug,
        description,
        "itemCount": count(*[_type == "item" && references(^._id) &&
          (freePlanStatus == "approved" || proPlanStatus == "success")])
      } | order(itemCount desc) [0...${limit}]`,
    });
  }

  // 标签搜索和自动补全
  static async searchTags(query: string): Promise<Tag[]> {
    if (query.length < 2) return [];

    return await sanityFetch<Tag[]>({
      query: `*[_type == "tag" && name match "*${query}*"] | order(name asc) [0...10] {
        _id,
        name,
        slug,
        "itemCount": count(*[_type == "item" && references(^._id)])
      }`,
    });
  }

  // 相关标签推荐
  static async getRelatedTags(tagIds: string[]): Promise<Tag[]> {
    if (tagIds.length === 0) return [];

    return await sanityFetch<Tag[]>({
      query: `*[_type == "tag" && !(_id in $tagIds)] {
        _id,
        name,
        slug,
        "commonItems": count(*[
          _type == "item" &&
          count(tags[@._ref in $tagIds]) > 0 &&
          references(^._id)
        ])
      } | order(commonItems desc) [0...5]`,
      params: { tagIds },
    });
  }

  // 创建新标签
  static async createTag(name: string): Promise<Tag> {
    const slug = slugify(name);

    // 检查是否已存在
    const existing = await sanityFetch<Tag>({
      query: `*[_type == "tag" && slug.current == $slug][0]`,
      params: { slug },
    });

    if (existing) {
      return existing;
    }

    // 创建新标签
    return await sanityClient.create({
      _type: 'tag',
      name,
      slug: { _type: 'slug', current: slug },
      description: `${name}相关的项目`,
    });
  }
}

// 标签输入组件
export function TagInput({
  value = [],
  onChange
}: {
  value: string[];
  onChange: (tags: string[]) => void;
}) {
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState<Tag[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 搜索标签建议
  const searchTags = useMemo(
    () => debounce(async (query: string) => {
      if (query.length < 2) {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await TagService.searchTags(query);
        setSuggestions(results);
      } finally {
        setIsLoading(false);
      }
    }, 300),
    []
  );

  useEffect(() => {
    searchTags(inputValue);
  }, [inputValue, searchTags]);

  const addTag = async (tagName: string) => {
    if (value.length >= 5) {
      toast.error('最多只能选择5个标签');
      return;
    }

    try {
      const tag = await TagService.createTag(tagName);
      if (!value.includes(tag._id)) {
        onChange([...value, tag._id]);
      }
      setInputValue('');
      setSuggestions([]);
    } catch (error) {
      toast.error('添加标签失败');
    }
  };

  const removeTag = (tagId: string) => {
    onChange(value.filter(id => id !== tagId));
  };

  return (
    <div className="space-y-2">
      {/* 已选标签 */}
      <div className="flex flex-wrap gap-2">
        {value.map(tagId => (
          <TagBadge
            key={tagId}
            tagId={tagId}
            onRemove={() => removeTag(tagId)}
          />
        ))}
      </div>

      {/* 输入框 */}
      <div className="relative">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && inputValue.trim()) {
              e.preventDefault();
              addTag(inputValue.trim());
            }
          }}
          placeholder="输入标签名称..."
          className="w-full px-3 py-2 border rounded-md"
        />

        {/* 建议列表 */}
        {suggestions.length > 0 && (
          <div className="absolute z-10 w-full mt-1 bg-white border rounded-md shadow-lg">
            {suggestions.map(tag => (
              <button
                key={tag._id}
                onClick={() => addTag(tag.name)}
                className="w-full px-3 py-2 text-left hover:bg-gray-100"
              >
                <span className="font-medium">{tag.name}</span>
                <span className="text-sm text-gray-500 ml-2">
                  ({tag.itemCount} 个项目)
                </span>
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
```

#### 2.2.3 搜索与发现
**功能描述**: 强大的搜索和内容发现功能
**业务价值**: 提升用户体验，增加页面停留时间

**技术实现详解**:

##### 搜索查询构建
```typescript
// 搜索参数处理
interface SearchParams {
  query?: string;      // 搜索关键词
  category?: string;   // 分类筛选
  tag?: string;        // 标签筛选
  sort?: string;       // 排序方式
  page?: number;       // 页码
  filter?: string;     // 其他筛选条件
}

// 构建GROQ查询语句
function buildSearchQuery(params: SearchParams) {
  let conditions = ['_type == "item"', 'defined(slug.current)'];
  let orderBy = 'order(publishDate desc)';

  // 1. 关键词搜索
  if (params.query) {
    const searchCondition = `(
      name match "*${params.query}*" ||
      description match "*${params.query}*" ||
      pt::text(introduction) match "*${params.query}*"
    )`;
    conditions.push(searchCondition);
  }

  // 2. 分类筛选
  if (params.category) {
    conditions.push(`references(*[_type=="category" && slug.current=="${params.category}"]._id)`);
  }

  // 3. 标签筛选
  if (params.tag) {
    conditions.push(`references(*[_type=="tag" && slug.current=="${params.tag}"]._id)`);
  }

  // 4. 状态筛选（只显示已发布）
  conditions.push('freePlanStatus == "approved" || proPlanStatus == "success"');

  // 5. 排序逻辑
  const sortOptions = {
    'newest': 'order(publishDate desc)',
    'oldest': 'order(publishDate asc)',
    'popular': 'order(featured desc, publishDate desc)',
    'random': 'order(_createdAt desc)' // 简化的随机排序
  };
  orderBy = sortOptions[params.sort] || sortOptions.newest;

  // 6. 分页计算
  const page = params.page || 1;
  const itemsPerPage = 12;
  const offset = (page - 1) * itemsPerPage;

  const whereClause = conditions.join(' && ');

  return {
    countQuery: `count(*[${whereClause}])`,
    dataQuery: `*[${whereClause}] | ${orderBy} [${offset}...${offset + itemsPerPage}] {
      _id,
      name,
      slug,
      description,
      link,
      image {
        ...,
        "blurDataURL": asset->metadata.lqip,
        "imageColor": asset->metadata.palette.dominant.background,
      },
      publishDate,
      featured,
      paid,
      categories[]->,
      tags[]->,
      submitter->
    }`
  };
}
```

##### 搜索建议与自动补全
```typescript
// 搜索建议API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');

  if (!query || query.length < 2) {
    return NextResponse.json({ suggestions: [] });
  }

  try {
    // 1. 从项目名称中获取建议
    const itemSuggestions = await sanityFetch({
      query: `*[_type == "item" && name match "*${query}*"][0...5] {
        "suggestion": name,
        "type": "item"
      }`
    });

    // 2. 从分类中获取建议
    const categorySuggestions = await sanityFetch({
      query: `*[_type == "category" && name match "*${query}*"][0...3] {
        "suggestion": name,
        "type": "category"
      }`
    });

    // 3. 从标签中获取建议
    const tagSuggestions = await sanityFetch({
      query: `*[_type == "tag" && name match "*${query}*"][0...3] {
        "suggestion": name,
        "type": "tag"
      }`
    });

    const allSuggestions = [
      ...itemSuggestions,
      ...categorySuggestions,
      ...tagSuggestions
    ];

    return NextResponse.json({ suggestions: allSuggestions });
  } catch (error) {
    return NextResponse.json({ suggestions: [] });
  }
}
```

##### 推荐算法实现
```typescript
// 相关项目推荐
async function getRelatedItems(currentItemId: string, limit: number = 4) {
  // 1. 获取当前项目信息
  const currentItem = await sanityFetch({
    query: `*[_type == "item" && _id == "${currentItemId}"][0] {
      categories[]._ref,
      tags[]._ref
    }`
  });

  if (!currentItem) return [];

  // 2. 基于分类和标签的相似度计算
  const relatedQuery = `*[
    _type == "item" &&
    _id != "${currentItemId}" &&
    (freePlanStatus == "approved" || proPlanStatus == "success") &&
    (
      count(categories[@._ref in [${currentItem.categories.map(c => `"${c._ref}"`).join(',')}]]) > 0 ||
      count(tags[@._ref in [${currentItem.tags.map(t => `"${t._ref}"`).join(',')}]]) > 0
    )
  ] | order(
    count(categories[@._ref in [${currentItem.categories.map(c => `"${c._ref}"`).join(',')}]]) desc,
    count(tags[@._ref in [${currentItem.tags.map(t => `"${t._ref}"`).join(',')}]]) desc,
    publishDate desc
  ) [0...${limit}] {
    _id,
    name,
    slug,
    description,
    image,
    categories[]->,
    tags[]->
  }`;

  return await sanityFetch({ query: relatedQuery });
}

// 热门搜索词统计
async function updateSearchStats(query: string) {
  // 这里可以集成分析工具或自建统计
  if (typeof window !== 'undefined') {
    // 客户端统计
    const searches = JSON.parse(localStorage.getItem('searchHistory') || '[]');
    searches.unshift(query);
    localStorage.setItem('searchHistory', JSON.stringify(searches.slice(0, 10)));
  }

  // 服务端可以记录到数据库或分析服务
  // await analytics.track('search', { query, timestamp: new Date() });
}
```

### 2.3 内容管理系统

#### 2.3.1 Headless CMS 集成
**功能描述**: 基于 Sanity 的无头内容管理系统
**业务价值**: 灵活的内容管理，支持多渠道发布

**技术实现详解**:

##### Sanity Studio 配置
```typescript
// sanity.config.ts
import { defineConfig } from 'sanity';
import { structureTool } from 'sanity/structure';
import { visionTool } from '@sanity/vision';
import { media } from 'sanity-plugin-media';
import { markdownSchema } from 'sanity-plugin-markdown';

export default defineConfig({
  name: 'Studio',
  basePath: '/studio',
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET!,

  plugins: [
    structureTool({
      structure: (S) =>
        S.list()
          .title('内容管理')
          .items([
            // 目录管理
            S.listItem()
              .title('项目管理')
              .child(
                S.list()
                  .title('项目')
                  .items([
                    S.listItem()
                      .title('待审核项目')
                      .child(
                        S.documentList()
                          .title('待审核项目')
                          .filter('_type == "item" && freePlanStatus == "pending"')
                      ),
                    S.listItem()
                      .title('已发布项目')
                      .child(
                        S.documentList()
                          .title('已发布项目')
                          .filter('_type == "item" && (freePlanStatus == "approved" || proPlanStatus == "success")')
                      ),
                    S.divider(),
                    ...S.documentTypeListItems().filter(listItem =>
                      ['item', 'category', 'tag'].includes(listItem.getId()!)
                    ),
                  ])
              ),

            // 博客管理
            S.listItem()
              .title('博客管理')
              .child(
                S.list()
                  .title('博客')
                  .items([
                    S.listItem()
                      .title('精选文章')
                      .child(
                        S.documentList()
                          .title('精选文章')
                          .filter('_type == "blogPost" && featured == true')
                      ),
                    S.divider(),
                    ...S.documentTypeListItems().filter(listItem =>
                      ['blogPost', 'blogCategory'].includes(listItem.getId()!)
                    ),
                  ])
              ),

            // 用户和订单管理
            S.listItem()
              .title('用户管理')
              .child(
                S.list()
                  .title('用户')
                  .items([
                    S.listItem()
                      .title('管理员用户')
                      .child(
                        S.documentList()
                          .title('管理员')
                          .filter('_type == "user" && role == "ADMIN"')
                      ),
                    S.listItem()
                      .title('普通用户')
                      .child(
                        S.documentList()
                          .title('用户')
                          .filter('_type == "user" && role == "USER"')
                      ),
                    S.divider(),
                    ...S.documentTypeListItems().filter(listItem =>
                      ['user', 'order'].includes(listItem.getId()!)
                    ),
                  ])
              ),

            // 系统设置
            S.listItem()
              .title('系统设置')
              .child(
                S.document()
                  .schemaType('settings')
                  .documentId('settings')
              ),
          ])
    }),
    visionTool(), // GROQ 查询工具
    media(), // 媒体管理插件
    markdownSchema(), // Markdown 支持
  ],

  schema: {
    types: schemaTypes,
  },
});
```

##### 内容查询和缓存
```typescript
// 内容查询服务
export class ContentService {
  private static cache = new Map<string, { data: any; timestamp: number }>();
  private static CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  // 带缓存的查询
  static async fetchWithCache<T>(
    query: string,
    params?: any,
    tags?: string[]
  ): Promise<T> {
    const cacheKey = `${query}-${JSON.stringify(params)}`;
    const cached = this.cache.get(cacheKey);

    // 检查缓存
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data;
    }

    // 从 Sanity 获取数据
    const data = await sanityFetch<T>({
      query,
      params,
      tags, // Next.js 缓存标签
    });

    // 更新缓存
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now(),
    });

    return data;
  }

  // 清除缓存
  static clearCache(pattern?: string) {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }
}

// 实时预览配置
export function useLiveQuery<T>(
  query: string,
  params?: any,
  initialData?: T
): T {
  const [data, setData] = useState<T>(initialData);

  useEffect(() => {
    const subscription = sanityClient
      .listen(query, params)
      .subscribe((update) => {
        if (update.result) {
          setData(update.result);
        }
      });

    return () => subscription.unsubscribe();
  }, [query, params]);

  return data;
}
```

#### 2.3.2 媒体资源管理
**功能描述**: 完整的文件上传、处理和管理系统
**业务价值**: 优化用户体验，降低存储成本

**技术实现详解**:

##### 图片上传和处理
```typescript
// 图片上传处理服务
export class MediaService {
  // 图片压缩和优化
  static async processImage(file: File): Promise<{
    original: File;
    compressed: File;
    thumbnail: File;
  }> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const img = new Image();

    return new Promise((resolve, reject) => {
      img.onload = () => {
        // 原图尺寸
        const { width, height } = img;

        // 压缩主图 (最大 1200px)
        const maxSize = 1200;
        const ratio = Math.min(maxSize / width, maxSize / height);
        const compressedWidth = width * ratio;
        const compressedHeight = height * ratio;

        canvas.width = compressedWidth;
        canvas.height = compressedHeight;
        ctx.drawImage(img, 0, 0, compressedWidth, compressedHeight);

        canvas.toBlob((compressedBlob) => {
          if (!compressedBlob) {
            reject(new Error('压缩失败'));
            return;
          }

          // 生成缩略图 (300x200)
          canvas.width = 300;
          canvas.height = 200;
          ctx.drawImage(img, 0, 0, 300, 200);

          canvas.toBlob((thumbnailBlob) => {
            if (!thumbnailBlob) {
              reject(new Error('缩略图生成失败'));
              return;
            }

            resolve({
              original: file,
              compressed: new File([compressedBlob], file.name, {
                type: 'image/jpeg',
              }),
              thumbnail: new File([thumbnailBlob], `thumb_${file.name}`, {
                type: 'image/jpeg',
              }),
            });
          }, 'image/jpeg', 0.8);
        }, 'image/jpeg', 0.9);
      };

      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });
  }

  // 批量上传
  static async uploadMultiple(files: File[]): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    const batchSize = 3; // 并发限制

    for (let i = 0; i < files.length; i += batchSize) {
      const batch = files.slice(i, i + batchSize);
      const batchPromises = batch.map(async (file) => {
        try {
          const processed = await this.processImage(file);
          const asset = await sanityClient.assets.upload('image', processed.compressed);
          return { success: true, asset, file };
        } catch (error) {
          return { success: false, error: error.message, file };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults.map(r =>
        r.status === 'fulfilled' ? r.value : { success: false, error: 'Upload failed' }
      ));
    }

    return results;
  }
}

// 拖拽上传组件
export function DragDropUpload({ onUpload }: { onUpload: (files: File[]) => void }) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);

    const files = Array.from(e.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );

    if (files.length > 0) {
      onUpload(files);
    }
  }, [onUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsDragging(false);
  }, []);

  return (
    <div
      className={cn(
        "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
        isDragging ? "border-primary bg-primary/10" : "border-gray-300"
      )}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
    >
      <Upload className="mx-auto h-12 w-12 text-gray-400" />
      <p className="mt-2 text-sm text-gray-600">
        拖拽图片到此处，或点击选择文件
      </p>
      <p className="text-xs text-gray-500">
        支持 JPG、PNG、WebP 格式，最大 5MB
      </p>
    </div>
  );
}
```

### 2.4 支付与商业化功能

#### 2.4.1 付费计划管理
**功能描述**: 基于 Stripe 的完整支付解决方案
**业务价值**: 平台主要收入来源，支持可持续发展

**详细需求**:
- **计划类型**:
  - 免费计划: $0，基础功能，需要反向链接
  - 专业计划: $9.9，高级功能，无反向链接要求
- **付费流程**:
  - 选择计划 → Stripe 支付 → 自动激活
  - 支持信用卡、借记卡支付
  - 支付失败自动重试机制
- **订单管理**:
  - 订单状态跟踪
  - 支付历史记录
  - 发票自动生成
  - 退款处理流程

#### 2.4.2 Stripe 集成
**功能描述**: 专业级支付处理集成
**业务价值**: 安全可靠的支付体验，降低支付摩擦

**技术实现详解**:

##### 支付流程完整实现
```typescript
// 1. 创建Stripe Checkout会话
export async function createCheckoutSession(itemId: string, priceId: string) {
  try {
    // 验证用户身份
    const user = await currentUser();
    if (!user?.id || !user?.email) {
      return { status: "error", message: "用户未登录" };
    }

    // 获取项目信息
    const item = await sanityFetch({
      query: itemByIdQuery,
      params: { id: itemId }
    });

    if (!item) {
      return { status: "error", message: "项目不存在" };
    }

    // 验证用户权限（只能为自己的项目付费）
    if (item.submitter._id !== user.id) {
      return { status: "error", message: "无权限操作" };
    }

    // 获取或创建Stripe客户
    let stripeCustomerId = await getOrCreateStripeCustomer(user);

    // 检查是否已经付费
    if (item.paid) {
      // 重定向到客户门户
      const billingSession = await stripe.billingPortal.sessions.create({
        customer: stripeCustomerId,
        return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard`,
      });
      redirect(billingSession.url);
    }

    // 创建Checkout会话
    const session = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      mode: "payment",
      line_items: [{
        price: priceId,
        quantity: 1,
      }],
      metadata: {
        userId: user.id,
        itemId: itemId,
        userEmail: user.email,
        itemName: item.name,
      },
      success_url: `${process.env.NEXT_PUBLIC_APP_URL}/publish/${itemId}?payment=success&session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.NEXT_PUBLIC_APP_URL}/payment/${itemId}?payment=cancelled`,
      payment_method_types: ["card"],
      billing_address_collection: "auto",
      // 启用客户门户
      customer_update: {
        address: "auto",
        name: "auto",
      },
    });

    // 重定向到Stripe Checkout
    redirect(session.url!);

  } catch (error) {
    console.error("创建支付会话失败:", error);
    return { status: "error", message: "支付初始化失败" };
  }
}

// 获取或创建Stripe客户
async function getOrCreateStripeCustomer(user: any): Promise<string> {
  // 检查用户是否已有Stripe客户ID
  const sanityUser = await getUserById(user.id);

  if (sanityUser?.stripeCustomerId) {
    return sanityUser.stripeCustomerId;
  }

  // 创建新的Stripe客户
  const customer = await stripe.customers.create({
    email: user.email,
    name: user.name,
    metadata: {
      sanityUserId: user.id,
    },
  });

  // 保存到Sanity
  await sanityClient
    .patch(user.id)
    .set({ stripeCustomerId: customer.id })
    .commit();

  return customer.id;
}
```

##### Webhook事件处理
```typescript
// Stripe Webhook处理: /api/webhook
export async function POST(req: Request) {
  const body = await req.text();
  const signature = headers().get("Stripe-Signature") as string;

  let event: Stripe.Event;

  try {
    // 验证Webhook签名
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error("Webhook签名验证失败:", err);
    return new Response("Webhook签名无效", { status: 400 });
  }

  // 处理不同类型的事件
  switch (event.type) {
    case "checkout.session.completed":
      await handleCheckoutCompleted(event.data.object as Stripe.Checkout.Session);
      break;

    case "payment_intent.succeeded":
      await handlePaymentSucceeded(event.data.object as Stripe.PaymentIntent);
      break;

    case "payment_intent.payment_failed":
      await handlePaymentFailed(event.data.object as Stripe.PaymentIntent);
      break;

    case "customer.subscription.deleted":
      // 处理订阅取消（如果有订阅功能）
      break;

    default:
      console.log(`未处理的事件类型: ${event.type}`);
  }

  return new Response("OK", { status: 200 });
}

// 处理支付成功
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  const { userId, itemId, userEmail, itemName } = session.metadata!;

  try {
    // 1. 创建订单记录
    const order = await sanityClient.create({
      _type: "order",
      user: { _type: "reference", _ref: userId },
      item: { _type: "reference", _ref: itemId },
      status: "success",
      date: new Date().toISOString(),
      stripeSessionId: session.id,
      amount: session.amount_total! / 100, // 转换为元
    });

    // 2. 更新项目状态
    await sanityClient
      .patch(itemId)
      .set({
        paid: true,
        featured: true, // 付费项目自动设为精选
        pricePlan: PricePlans.PRO,
        proPlanStatus: ProPlanStatus.SUCCESS,
        order: { _type: "reference", _ref: order._id },
        publishDate: new Date().toISOString(), // 立即发布
      })
      .commit();

    // 3. 发送确认邮件
    const itemLink = `${process.env.NEXT_PUBLIC_APP_URL}/item/${itemId}`;
    await sendPaymentSuccessEmail(userEmail, itemName, itemLink);

    // 4. 清除相关缓存
    revalidatePath('/');
    revalidatePath('/search');
    revalidatePath(`/item/${itemId}`);

    console.log(`支付成功处理完成: 用户${userId}, 项目${itemId}`);

  } catch (error) {
    console.error("处理支付成功事件失败:", error);
    // 这里可以添加重试机制或错误通知
  }
}

// 处理支付失败
async function handlePaymentFailed(paymentIntent: Stripe.PaymentIntent) {
  const { userId, itemId } = paymentIntent.metadata!;

  try {
    // 记录支付失败
    await sanityClient.create({
      _type: "order",
      user: { _type: "reference", _ref: userId },
      item: { _type: "reference", _ref: itemId },
      status: "failed",
      date: new Date().toISOString(),
      stripePaymentIntentId: paymentIntent.id,
      failureReason: paymentIntent.last_payment_error?.message,
    });

    // 可以发送支付失败通知邮件
    console.log(`支付失败: 用户${userId}, 项目${itemId}`);

  } catch (error) {
    console.error("处理支付失败事件失败:", error);
  }
}
```

##### 支付状态查询
```typescript
// 查询支付状态API
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const sessionId = searchParams.get('session_id');

  if (!sessionId) {
    return NextResponse.json({ error: "缺少session_id" }, { status: 400 });
  }

  try {
    // 从Stripe获取会话信息
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    return NextResponse.json({
      status: session.payment_status,
      customerEmail: session.customer_details?.email,
      amountTotal: session.amount_total,
      currency: session.currency,
    });

  } catch (error) {
    return NextResponse.json({ error: "查询失败" }, { status: 500 });
  }
}
```

**安全措施实现**:
- **Webhook签名验证**: 确保事件来自Stripe
- **元数据验证**: 验证用户权限和项目所有权
- **幂等性处理**: 防止重复处理同一事件
- **错误处理**: 完善的错误日志和重试机制
- **数据加密**: 敏感信息加密存储

### 2.5 管理员审核系统

#### 2.5.1 项目审核流程
**功能描述**: 管理员审核用户提交项目的完整工作流
**业务价值**: 确保平台内容质量，维护用户体验

**技术实现详解**:

##### 审核队列管理
```typescript
// 获取待审核项目列表
export async function getPendingItems(page: number = 1, limit: number = 20) {
  const offset = (page - 1) * limit;

  const query = `{
    "items": *[
      _type == "item" &&
      freePlanStatus == "pending"
    ] | order(_createdAt desc) [${offset}...${offset + limit}] {
      _id,
      _createdAt,
      name,
      slug,
      description,
      link,
      image,
      submitter-> {
        _id,
        name,
        email
      },
      categories[]-> {
        name,
        slug
      },
      tags[]-> {
        name,
        slug
      }
    },
    "total": count(*[_type == "item" && freePlanStatus == "pending"])
  }`;

  return await sanityFetch({ query });
}

// 审核决策处理
export async function reviewItemAction(
  itemId: string,
  decision: 'approve' | 'reject',
  rejectionReason?: string
) {
  try {
    // 1. 验证管理员权限
    const user = await currentUser();
    if (!user || user.role !== 'ADMIN') {
      return { status: "error", message: "无权限操作" };
    }

    // 2. 获取项目信息
    const item = await getItemById(itemId);
    if (!item) {
      return { status: "error", message: "项目不存在" };
    }

    // 3. 检查当前状态
    if (item.freePlanStatus !== FreePlanStatus.PENDING) {
      return { status: "error", message: "项目状态不允许审核" };
    }

    let updateData: any = {
      reviewedAt: new Date().toISOString(),
      reviewedBy: {
        _type: "reference",
        _ref: user.id,
      },
    };

    if (decision === 'approve') {
      updateData = {
        ...updateData,
        freePlanStatus: FreePlanStatus.APPROVED,
        publishDate: new Date().toISOString(),
        rejectionReason: null,
      };
    } else {
      updateData = {
        ...updateData,
        freePlanStatus: FreePlanStatus.REJECTED,
        rejectionReason: rejectionReason || "不符合平台要求",
        publishDate: null,
      };
    }

    // 4. 更新项目状态
    const result = await sanityClient
      .patch(itemId)
      .set(updateData)
      .commit();

    // 5. 发送通知邮件
    await sendReviewResultEmail(item, decision, rejectionReason);

    // 6. 清除缓存
    if (decision === 'approve') {
      revalidatePath('/');
      revalidatePath('/search');
      revalidatePath(`/category/${item.categories?.[0]?.slug}`);
    }

    return {
      status: "success",
      message: decision === 'approve' ? "项目已通过审核" : "项目已拒绝"
    };

  } catch (error) {
    console.error("审核操作失败:", error);
    return { status: "error", message: "操作失败，请重试" };
  }
}
```

##### 批量审核功能
```typescript
// 批量审核处理
export async function batchReviewAction(
  itemIds: string[],
  decision: 'approve' | 'reject',
  rejectionReason?: string
) {
  try {
    const user = await currentUser();
    if (!user || user.role !== 'ADMIN') {
      return { status: "error", message: "无权限操作" };
    }

    const results = [];

    // 并发处理，但限制并发数
    const batchSize = 5;
    for (let i = 0; i < itemIds.length; i += batchSize) {
      const batch = itemIds.slice(i, i + batchSize);
      const batchPromises = batch.map(itemId =>
        reviewItemAction(itemId, decision, rejectionReason)
      );

      const batchResults = await Promise.allSettled(batchPromises);
      results.push(...batchResults);
    }

    const successCount = results.filter(r =>
      r.status === 'fulfilled' && r.value.status === 'success'
    ).length;

    return {
      status: "success",
      message: `批量操作完成，成功处理 ${successCount}/${itemIds.length} 个项目`
    };

  } catch (error) {
    console.error("批量审核失败:", error);
    return { status: "error", message: "批量操作失败" };
  }
}
```

##### 审核统计和报告
```typescript
// 获取审核统计数据
export async function getReviewStats(timeRange: 'week' | 'month' | 'year' = 'month') {
  const now = new Date();
  let startDate: Date;

  switch (timeRange) {
    case 'week':
      startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case 'month':
      startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case 'year':
      startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
  }

  const query = `{
    "pending": count(*[_type == "item" && freePlanStatus == "pending"]),
    "approved": count(*[
      _type == "item" &&
      freePlanStatus == "approved" &&
      reviewedAt > "${startDate.toISOString()}"
    ]),
    "rejected": count(*[
      _type == "item" &&
      freePlanStatus == "rejected" &&
      reviewedAt > "${startDate.toISOString()}"
    ]),
    "totalSubmissions": count(*[
      _type == "item" &&
      _createdAt > "${startDate.toISOString()}"
    ]),
    "avgReviewTime": avg(*[
      _type == "item" &&
      defined(reviewedAt) &&
      reviewedAt > "${startDate.toISOString()}"
    ].reviewTime),
    "topCategories": *[_type == "category"] {
      name,
      "submissionCount": count(*[
        _type == "item" &&
        references(^._id) &&
        _createdAt > "${startDate.toISOString()}"
      ])
    } | order(submissionCount desc) [0...5]
  }`;

  return await sanityFetch({ query });
}
```

### 2.6 邮件通知系统

#### 2.6.1 事务性邮件
**功能描述**: 基于 Resend 的邮件发送服务
**业务价值**: 提升用户体验，增强平台可信度

**技术实现详解**:

##### 邮件发送核心逻辑
```typescript
// 邮件发送基础服务
export const resend = new Resend(process.env.RESEND_API_KEY);

// 发送审核结果邮件
export async function sendReviewResultEmail(
  item: any,
  decision: 'approve' | 'reject',
  rejectionReason?: string
) {
  const submitter = item.submitter;

  try {
    if (decision === 'approve') {
      // 发送通过邮件
      await resend.emails.send({
        from: process.env.RESEND_EMAIL_FROM!,
        to: submitter.email,
        subject: `🎉 您的项目"${item.name}"已通过审核`,
        react: ApprovalEmail({
          userName: submitter.name,
          itemName: item.name,
          itemLink: `${process.env.NEXT_PUBLIC_APP_URL}/item/${item.slug.current}`,
        }),
      });
    } else {
      // 发送拒绝邮件
      await resend.emails.send({
        from: process.env.RESEND_EMAIL_FROM!,
        to: submitter.email,
        subject: `关于您的项目"${item.name}"的审核结果`,
        react: RejectionEmail({
          userName: submitter.name,
          itemName: item.name,
          rejectionReason: rejectionReason || "不符合平台要求",
          resubmitLink: `${process.env.NEXT_PUBLIC_APP_URL}/edit/${item._id}`,
        }),
      });
    }
  } catch (error) {
    console.error("发送审核结果邮件失败:", error);
    // 邮件发送失败不应该影响审核流程
  }
}

// 邮件模板组件
export function ApprovalEmail({ userName, itemName, itemLink }: {
  userName: string;
  itemName: string;
  itemLink: string;
}) {
  return (
    <Html>
      <Head />
      <Preview>您的项目已通过审核</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={box}>
            <Img src={`${baseUrl}/logo.png`} width="32" height="32" alt="Logo" />
            <Hr style={hr} />
            <Text style={paragraph}>Hi {userName},</Text>
            <Text style={paragraph}>
              恭喜！您提交的项目 <strong>{itemName}</strong> 已通过我们的审核，现已在平台上线。
            </Text>
            <Button style={button} href={itemLink}>
              查看项目
            </Button>
            <Text style={paragraph}>
              感谢您对我们平台的支持！
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
}
```

#### 2.6.2 Newsletter 订阅系统
```typescript
// Newsletter 订阅处理
export async function subscribeToNewsletter(email: string) {
  try {
    // 1. 添加到Resend受众列表
    const result = await resend.contacts.create({
      email,
      unsubscribed: false,
      audienceId: process.env.RESEND_AUDIENCE_ID!,
    });

    if (result.error) {
      return { status: "error", message: "订阅失败" };
    }

    // 2. 发送欢迎邮件
    await resend.emails.send({
      from: process.env.RESEND_EMAIL_FROM!,
      to: email,
      subject: "欢迎订阅我们的Newsletter！",
      react: NewsletterWelcomeEmail({ email }),
    });

    return { status: "success", message: "订阅成功！" };

  } catch (error) {
    console.error("Newsletter订阅失败:", error);
    return { status: "error", message: "订阅失败，请重试" };
  }
}

// 退订处理
export async function unsubscribeFromNewsletter(email: string) {
  try {
    await resend.contacts.update({
      email,
      unsubscribed: true,
      audienceId: process.env.RESEND_AUDIENCE_ID!,
    });

    return { status: "success", message: "已成功退订" };
  } catch (error) {
    return { status: "error", message: "退订失败" };
  }
}
```

### 2.7 博客系统

#### 2.7.1 博客内容管理
**功能描述**: 完整的博客发布和管理系统
**业务价值**: 提升SEO效果，增加用户粘性

**技术实现详解**:

##### 博客文章管理
```typescript
// 博客数据服务
export class BlogService {
  // 获取博客文章列表
  static async getBlogPosts({
    category,
    featured,
    page = 1,
    limit = 10,
  }: {
    category?: string;
    featured?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{ posts: BlogPost[]; total: number }> {
    let conditions = ['_type == "blogPost"', 'defined(slug.current)', 'defined(publishDate)'];

    if (category) {
      conditions.push(`references(*[_type=="blogCategory" && slug.current=="${category}"]._id)`);
    }

    if (featured !== undefined) {
      conditions.push(`featured == ${featured}`);
    }

    const whereClause = conditions.join(' && ');
    const offset = (page - 1) * limit;

    const [posts, total] = await Promise.all([
      sanityFetch<BlogPost[]>({
        query: `*[${whereClause}] | order(publishDate desc) [${offset}...${offset + limit}] {
          _id,
          title,
          slug,
          excerpt,
          featured,
          publishDate,
          image {
            ...,
            "blurDataURL": asset->metadata.lqip,
            "imageColor": asset->metadata.palette.dominant.background,
          },
          author-> {
            _id,
            name,
            image
          },
          categories[]-> {
            _id,
            name,
            slug
          },
          "readingTime": round(length(pt::text(body)) / 5 / 180)
        }`,
      }),
      sanityFetch<number>({
        query: `count(*[${whereClause}])`,
      }),
    ]);

    return { posts, total };
  }

  // 获取单篇博客文章
  static async getBlogPost(slug: string): Promise<BlogPost | null> {
    return await sanityFetch<BlogPost>({
      query: `*[_type == "blogPost" && slug.current == $slug][0] {
        _id,
        title,
        slug,
        excerpt,
        featured,
        publishDate,
        body,
        image {
          ...,
          "blurDataURL": asset->metadata.lqip,
          "imageColor": asset->metadata.palette.dominant.background,
        },
        author-> {
          _id,
          name,
          image,
          link
        },
        categories[]-> {
          _id,
          name,
          slug,
          description
        },
        relatedPosts[]-> {
          _id,
          title,
          slug,
          excerpt,
          publishDate,
          image,
          author-> {
            name
          }
        },
        "readingTime": round(length(pt::text(body)) / 5 / 180),
        "wordCount": length(pt::text(body))
      }`,
      params: { slug },
    });
  }

  // 获取相关文章
  static async getRelatedPosts(postId: string, categoryIds: string[]): Promise<BlogPost[]> {
    return await sanityFetch<BlogPost[]>({
      query: `*[
        _type == "blogPost" &&
        _id != $postId &&
        defined(publishDate) &&
        count(categories[@._ref in $categoryIds]) > 0
      ] | order(publishDate desc) [0...3] {
        _id,
        title,
        slug,
        excerpt,
        publishDate,
        image,
        author-> {
          name
        }
      }`,
      params: { postId, categoryIds },
    });
  }
}

// 博客文章编辑器
export function BlogEditor({
  initialData,
  onSave
}: {
  initialData?: Partial<BlogPost>;
  onSave: (data: BlogPostFormData) => Promise<void>;
}) {
  const [formData, setFormData] = useState<BlogPostFormData>({
    title: initialData?.title || '',
    slug: initialData?.slug?.current || '',
    excerpt: initialData?.excerpt || '',
    body: initialData?.body || [],
    featured: initialData?.featured || false,
    categories: initialData?.categories?.map(c => c._id) || [],
    publishDate: initialData?.publishDate || new Date().toISOString(),
    imageId: initialData?.image?.asset?._ref || '',
  });

  const [isPreview, setIsPreview] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 自动生成slug
  useEffect(() => {
    if (formData.title && !initialData?.slug) {
      const slug = slugify(formData.title);
      setFormData(prev => ({ ...prev, slug }));
    }
  }, [formData.title, initialData?.slug]);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave(formData);
      toast.success('保存成功');
    } catch (error) {
      toast.error('保存失败');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">
          {initialData ? '编辑文章' : '新建文章'}
        </h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsPreview(!isPreview)}
          >
            {isPreview ? '编辑' : '预览'}
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? '保存中...' : '保存'}
          </Button>
        </div>
      </div>

      {isPreview ? (
        <BlogPreview data={formData} />
      ) : (
        <div className="space-y-6">
          {/* 标题 */}
          <div>
            <label className="block text-sm font-medium mb-2">标题</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                title: e.target.value
              }))}
              className="w-full px-3 py-2 border rounded-md"
              placeholder="输入文章标题..."
            />
          </div>

          {/* Slug */}
          <div>
            <label className="block text-sm font-medium mb-2">URL Slug</label>
            <input
              type="text"
              value={formData.slug}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                slug: e.target.value
              }))}
              className="w-full px-3 py-2 border rounded-md"
              placeholder="url-slug"
            />
          </div>

          {/* 摘要 */}
          <div>
            <label className="block text-sm font-medium mb-2">摘要</label>
            <textarea
              value={formData.excerpt}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                excerpt: e.target.value
              }))}
              rows={3}
              className="w-full px-3 py-2 border rounded-md"
              placeholder="文章摘要..."
            />
          </div>

          {/* 分类选择 */}
          <div>
            <label className="block text-sm font-medium mb-2">分类</label>
            <BlogCategorySelector
              value={formData.categories}
              onChange={(categories) => setFormData(prev => ({
                ...prev,
                categories
              }))}
            />
          </div>

          {/* 特色文章 */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="featured"
              checked={formData.featured}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                featured: e.target.checked
              }))}
            />
            <label htmlFor="featured" className="text-sm font-medium">
              设为特色文章
            </label>
          </div>

          {/* 富文本编辑器 */}
          <div>
            <label className="block text-sm font-medium mb-2">正文</label>
            <RichTextEditor
              value={formData.body}
              onChange={(body) => setFormData(prev => ({ ...prev, body }))}
            />
          </div>
        </div>
      )}
    </div>
  );
}
```

---

## 3. 用户界面设计

### 3.1 页面架构

#### 3.1.1 公开页面
- **首页** (`/`):
  - 英雄区域展示
  - 精选项目轮播
  - 热门分类网格
  - 最新项目列表
  - Newsletter 订阅
- **搜索页** (`/search`):
  - 搜索框和筛选器
  - 结果列表/网格视图
  - 分页导航
  - 排序选项
- **分类页** (`/category/[slug]`):
  - 分类介绍
  - 子分类导航
  - 项目列表
  - 相关分类推荐
- **项目详情** (`/item/[slug]`):
  - 项目信息展示
  - 截图画廊
  - 相关项目推荐
  - 社交分享按钮

#### 3.1.2 用户功能页面
- **仪表板** (`/dashboard`):
  - 数据概览卡片
  - 最近提交项目
  - 快速操作入口
  - 通知中心
- **项目提交** (`/submit`):
  - 分步骤表单
  - 实时验证反馈
  - 草稿保存功能
  - 预览功能
- **支付页面** (`/payment/[id]`):
  - 计划对比表格
  - 支付表单
  - 安全标识
  - 常见问题

### 3.2 设计系统

#### 3.2.1 视觉设计
- **色彩系统**: 基于 Tailwind CSS 的色彩规范
- **字体系统**: 系统字体栈，支持多语言
- **间距系统**: 8px 基础网格系统
- **组件库**: 基于 Shadcn/ui 的组件系统

#### 3.2.2 响应式设计
- **断点设置**:
  - Mobile: < 768px
  - Tablet: 768px - 1024px
  - Desktop: > 1024px
- **适配策略**: Mobile-first 设计方法
- **交互优化**: 触摸友好的交互设计

---

## 4. 开发环境配置

### 4.1 环境变量配置

#### 4.1.1 必需环境变量
```bash
# 应用基础配置
NEXT_PUBLIC_APP_URL=http://localhost:3000  # 生产环境改为实际域名

# Sanity CMS 配置
NEXT_PUBLIC_SANITY_DATASET=production      # 或 development
NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
SANITY_API_TOKEN=your_api_token            # 需要 Editor 权限

# 邮件服务配置 (Resend)
RESEND_API_KEY=re_your_api_key
RESEND_EMAIL_FROM=<EMAIL>
RESEND_EMAIL_ADMIN=<EMAIL>
RESEND_AUDIENCE_ID=your_audience_id        # Newsletter 受众ID

# 支付配置 (Stripe)
STRIPE_API_KEY=sk_test_your_secret_key     # 生产环境用 sk_live_
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
NEXT_PUBLIC_STRIPE_PRO_PRICE_ID=price_your_price_id

# 认证配置 (NextAuth)
AUTH_SECRET=your_32_char_secret            # openssl rand -base64 32
AUTH_TRUST_HOST=true                       # Docker 部署时需要

# OAuth 配置 (可选)
AUTH_GOOGLE_ID=your_google_client_id
AUTH_GOOGLE_SECRET=your_google_client_secret
AUTH_GITHUB_ID=your_github_client_id
AUTH_GITHUB_SECRET=your_github_client_secret
```

#### 4.1.2 环境变量获取指南

**Sanity 配置**:
1. 访问 [sanity.io/manage](https://sanity.io/manage)
2. 创建新项目或选择现有项目
3. 获取 Project ID 和 Dataset 名称
4. 在 API 设置中创建 Token（需要 Editor 权限）

**Resend 配置**:
1. 访问 [resend.com](https://resend.com)
2. 创建账户并验证域名
3. 在 API Keys 页面创建新的 API Key
4. 在 Audiences 页面创建受众列表获取 ID

**Stripe 配置**:
1. 访问 [stripe.com](https://stripe.com)
2. 在开发者设置中获取 API Keys
3. 创建产品和价格，获取 Price ID
4. 配置 Webhook 端点: `your-domain.com/api/webhook`
5. 选择事件: `checkout.session.completed`, `payment_intent.succeeded`

### 4.2 项目初始化步骤

#### 4.2.1 本地开发环境搭建
```bash
# 1. 克隆项目
git clone https://github.com/your-org/mkdirs-template.git
cd mkdirs-template

# 2. 安装依赖 (推荐使用 pnpm)
pnpm install

# 3. 复制环境变量模板
cp .env.example .env.local

# 4. 配置环境变量 (编辑 .env.local)
# 填入上述所有必需的环境变量

# 5. 生成 Sanity 类型定义
pnpm typegen

# 6. 启动开发服务器
pnpm dev

# 7. 启动 Sanity Studio (新终端)
# 访问 http://localhost:3000/studio

# 8. 启动邮件预览服务器 (可选)
pnpm email
# 访问 http://localhost:3333
```

#### 4.2.2 数据库初始化
```bash
# 1. 登录 Sanity CLI
npx sanity login

# 2. 部署 Schema 到 Sanity
npx sanity deploy

# 3. 创建初始管理员用户 (在 Sanity Studio 中)
# 访问 /studio -> Users -> Create new user
# 设置 role 为 "ADMIN"

# 4. 创建基础分类和标签 (在 Sanity Studio 中)
# 访问 /studio -> Categories/Tags -> Create new
```

### 4.3 中间件和路由保护

#### 4.3.1 认证中间件实现
```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

export async function middleware(request: NextRequest) {
  const token = await getToken({
    req: request,
    secret: process.env.AUTH_SECRET
  });

  const { pathname } = request.nextUrl;

  // 公开路由，无需认证
  const publicRoutes = [
    '/',
    '/search',
    '/category',
    '/item',
    '/blog',
    '/auth',
    '/api/auth',
    '/api/webhook',
    '/api/og',
  ];

  const isPublicRoute = publicRoutes.some(route =>
    pathname.startsWith(route)
  );

  // 管理员路由
  const adminRoutes = ['/studio', '/admin'];
  const isAdminRoute = adminRoutes.some(route =>
    pathname.startsWith(route)
  );

  // 受保护路由
  const protectedRoutes = ['/dashboard', '/submit', '/edit', '/payment', '/publish'];
  const isProtectedRoute = protectedRoutes.some(route =>
    pathname.startsWith(route)
  );

  // 检查认证状态
  if (isProtectedRoute || isAdminRoute) {
    if (!token) {
      const loginUrl = new URL('/auth/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // 检查管理员权限
    if (isAdminRoute && token.role !== 'ADMIN') {
      return NextResponse.redirect(new URL('/dashboard', request.url));
    }
  }

  // 速率限制
  if (pathname.startsWith('/api/')) {
    const ip = request.ip || 'unknown';
    const rateLimitResult = await checkRateLimit(ip, pathname);

    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: '请求过于频繁' },
        { status: 429 }
      );
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};

// 速率限制检查
async function checkRateLimit(
  ip: string,
  endpoint: string
): Promise<{ allowed: boolean; remaining: number }> {
  // 不同端点的限制策略
  const limits = {
    '/api/upload-image': { requests: 10, window: 60 }, // 每分钟10次
    '/api/auth': { requests: 5, window: 60 }, // 每分钟5次
    default: { requests: 100, window: 60 }, // 默认每分钟100次
  };

  const limit = limits[endpoint] || limits.default;
  const key = `rate_limit:${ip}:${endpoint}`;

  // 这里可以使用 Redis 或内存存储
  // 简化示例，实际应该使用 Redis
  return { allowed: true, remaining: limit.requests };
}
```

#### 4.3.2 API 路由保护
```typescript
// 高阶函数：API 路由保护
export function withAuth(handler: NextApiHandler, requiredRole?: 'ADMIN' | 'USER') {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    try {
      const session = await getServerSession(req, res, authConfig);

      if (!session?.user) {
        return res.status(401).json({ error: '未授权访问' });
      }

      if (requiredRole && session.user.role !== requiredRole) {
        return res.status(403).json({ error: '权限不足' });
      }

      // 将用户信息添加到请求对象
      (req as any).user = session.user;

      return handler(req, res);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return res.status(500).json({ error: '服务器错误' });
    }
  };
}

// 使用示例
export default withAuth(async function handler(req, res) {
  const user = (req as any).user;

  if (req.method === 'POST') {
    // 处理提交逻辑
    const result = await submitItem(req.body, user.id);
    return res.json(result);
  }

  return res.status(405).json({ error: '方法不允许' });
}, 'USER');
```

### 4.4 CI/CD 流水线配置

#### 4.4.1 GitHub Actions 配置
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install

      - name: Run type check
        run: pnpm type-check

      - name: Run linting
        run: pnpm lint

      - name: Run tests
        run: pnpm test
        env:
          NODE_ENV: test

      - name: Build application
        run: pnpm build
        env:
          NEXT_PUBLIC_APP_URL: ${{ secrets.NEXT_PUBLIC_APP_URL }}
          NEXT_PUBLIC_SANITY_PROJECT_ID: ${{ secrets.NEXT_PUBLIC_SANITY_PROJECT_ID }}
          NEXT_PUBLIC_SANITY_DATASET: ${{ secrets.NEXT_PUBLIC_SANITY_DATASET }}

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

      - name: Run E2E tests
        run: pnpm test:e2e
        env:
          BASE_URL: ${{ steps.deploy.outputs.preview-url }}

      - name: Notify deployment
        if: success()
        run: |
          curl -X POST ${{ secrets.SLACK_WEBHOOK_URL }} \
            -H 'Content-type: application/json' \
            --data '{"text":"🚀 Production deployment successful!"}'
```

#### 4.4.2 质量检查配置
```json
// package.json scripts
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "prepare": "husky install"
  }
}

// .husky/pre-commit
#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

pnpm lint-staged

// lint-staged.config.js
module.exports = {
  '*.{js,jsx,ts,tsx}': [
    'eslint --fix',
    'prettier --write',
  ],
  '*.{json,md,yml,yaml}': [
    'prettier --write',
  ],
};
```

### 4.5 部署配置

#### 4.5.1 Vercel 部署 (推荐)
```bash
# 1. 安装 Vercel CLI
npm i -g vercel

# 2. 登录 Vercel
vercel login

# 3. 部署项目
vercel

# 4. 配置环境变量
# 在 Vercel Dashboard -> Settings -> Environment Variables
# 添加所有生产环境变量

# 5. 配置域名
# 在 Vercel Dashboard -> Settings -> Domains
# 添加自定义域名并配置 DNS
```

#### 4.5.2 Docker 部署
```dockerfile
# 使用项目提供的 Dockerfile
docker build -t mkdirs-app .

# 运行容器
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_APP_URL=https://yourdomain.com \
  -e SANITY_API_TOKEN=your_token \
  # ... 其他环境变量
  mkdirs-app
```

#### 4.5.3 生产环境检查清单
- [ ] 所有环境变量已配置
- [ ] Stripe Webhook 端点已配置
- [ ] 邮件域名已验证
- [ ] Sanity CORS 设置已更新
- [ ] SSL 证书已配置
- [ ] 监控和日志已设置
- [ ] 备份策略已实施
- [ ] 性能监控已配置

---

## 5. 技术实现

### 5.1 API 接口设计

#### 5.1.1 认证接口
```typescript
// POST /api/auth/[...nextauth] - NextAuth.js 统一认证处理
// 请求体: { email: string, password: string }
// 响应: { user: User, session: Session } | { error: string }

// GET /api/auth/session - 获取当前用户会话
// 响应: { user: User, expires: string } | null

// POST /api/auth/signout - 用户登出
// 响应: { url: string }
```

#### 5.1.2 业务接口详细规范

**图片上传接口**
```typescript
// POST /api/upload-image
interface UploadImageRequest {
  file: File; // FormData 格式
}

interface UploadImageResponse {
  asset: {
    _id: string;
    url: string;
    metadata: {
      dimensions: { width: number; height: number };
      lqip: string; // 低质量图片占位符
    };
  };
}

// 限制条件:
// - 支持格式: JPG, PNG, WebP
// - 最大尺寸: 5MB
// - 自动压缩和优化
```

**Webhook 处理接口**
```typescript
// POST /api/webhook - Stripe Webhook 处理
interface WebhookRequest {
  type: string;
  data: {
    object: Stripe.Checkout.Session | Stripe.PaymentIntent;
  };
}

// 处理事件类型:
// - checkout.session.completed: 支付成功
// - payment_intent.succeeded: 支付确认
// - payment_intent.payment_failed: 支付失败
```

**OG 图片生成接口**
```typescript
// GET /api/og?title=xxx&description=xxx&type=xxx
interface OGImageParams {
  title: string;
  description?: string;
  type?: 'Item' | 'Category' | 'Blog Post';
}

// 响应: PNG 图片 (1200x630)
```

**搜索建议接口**
```typescript
// GET /api/search/suggestions?q=keyword
interface SearchSuggestionsResponse {
  suggestions: Array<{
    suggestion: string;
    type: 'item' | 'category' | 'tag';
  }>;
}
```

### 5.2 完整数据模型设计

#### 5.2.1 Sanity Schema 定义

**用户模型 (User)**
```typescript
interface User {
  _id: string;
  _type: 'user';
  _createdAt: string;
  _updatedAt: string;

  // 基本信息
  name: string;
  email: string;
  emailVerified?: string; // ISO datetime
  image?: string;
  link?: string;

  // 认证相关
  password?: string; // bcrypt 加密
  role: 'USER' | 'ADMIN';

  // 支付相关
  stripeCustomerId?: string;

  // 关联数据
  accounts?: Reference<Account>[];
}
```

**项目模型 (Item)**
```typescript
interface Item {
  _id: string;
  _type: 'item';
  _createdAt: string;
  _updatedAt: string;

  // 基本信息
  name: string;
  slug: Slug;
  description: string;
  introduction?: string; // 详细介绍，支持 Markdown
  link: string;

  // 媒体资源
  image: {
    _type: 'image';
    asset: Reference<SanityImageAsset>;
    alt: string;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
  };

  // 分类和标签
  categories: Reference<Category>[];
  tags: Reference<Tag>[];

  // 状态管理
  publishDate?: string; // ISO datetime
  featured: boolean;
  paid: boolean;

  // 计划和状态
  pricePlan: 'FREE' | 'PRO';
  freePlanStatus?: 'submitting' | 'pending' | 'approved' | 'rejected';
  proPlanStatus?: 'success' | 'failed';
  rejectionReason?: string;

  // 审核信息
  reviewedAt?: string; // ISO datetime
  reviewedBy?: Reference<User>;

  // 关联数据
  submitter: Reference<User>;
  order?: Reference<Order>;
}
```

**分类模型 (Category)**
```typescript
interface Category {
  _id: string;
  _type: 'category';
  _createdAt: string;
  _updatedAt: string;

  name: string;
  slug: Slug;
  description?: string;
  priority: number; // 排序优先级
}
```

**标签模型 (Tag)**
```typescript
interface Tag {
  _id: string;
  _type: 'tag';
  _createdAt: string;
  _updatedAt: string;

  name: string;
  slug: Slug;
  description?: string;
}
```

**订单模型 (Order)**
```typescript
interface Order {
  _id: string;
  _type: 'order';
  _createdAt: string;
  _updatedAt: string;

  user: Reference<User>;
  item: Reference<Item>;
  status: 'success' | 'failed';
  date: string; // ISO datetime

  // Stripe 相关
  stripeSessionId?: string;
  stripePaymentIntentId?: string;
  amount?: number; // 金额（分）
  currency?: string; // 货币代码
  failureReason?: string; // 失败原因
}
```

### 4.3 性能优化策略

#### 4.3.1 前端性能优化
```typescript
// 代码分割和懒加载
import { lazy, Suspense } from 'react';
import dynamic from 'next/dynamic';

// 路由级别代码分割
const DashboardPage = lazy(() => import('@/pages/dashboard'));
const SubmitPage = lazy(() => import('@/pages/submit'));

// 组件级别动态导入
const HeavyComponent = dynamic(() => import('@/components/HeavyComponent'), {
  loading: () => <div>加载中...</div>,
  ssr: false, // 禁用服务端渲染（如果组件很重）
});

// 图片优化配置
export function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false
}: ImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      priority={priority}
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Rq5TaUVZLDe2eIiuEAkuLd4pFDxOjFWU9iOhFEjjhiZZHUOjAMrKcgg8EEcEVBtBa5a8RvdQnvbqO1tlDzTyBFUdh4UAAAAAAAAAAAAAAAAAAAAAAAB//2Q=="
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      className="rounded-lg"
    />
  );
}

// 虚拟滚动（大列表优化）
export function VirtualizedList<T>({
  items,
  renderItem,
  itemHeight = 100
}: {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight?: number;
}) {
  const [startIndex, setStartIndex] = useState(0);
  const [endIndex, setEndIndex] = useState(10);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const scrollTop = container.scrollTop;
      const containerHeight = container.clientHeight;

      const newStartIndex = Math.floor(scrollTop / itemHeight);
      const newEndIndex = Math.min(
        newStartIndex + Math.ceil(containerHeight / itemHeight) + 1,
        items.length
      );

      setStartIndex(newStartIndex);
      setEndIndex(newEndIndex);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [itemHeight, items.length]);

  const visibleItems = items.slice(startIndex, endIndex);

  return (
    <div
      ref={containerRef}
      className="h-96 overflow-auto"
      style={{ height: '400px' }}
    >
      <div style={{ height: startIndex * itemHeight }} />
      {visibleItems.map((item, index) =>
        renderItem(item, startIndex + index)
      )}
      <div style={{ height: (items.length - endIndex) * itemHeight }} />
    </div>
  );
}
```

#### 4.3.2 缓存策略实现
```typescript
// 多层缓存系统
export class CacheManager {
  private static memoryCache = new Map<string, { data: any; expires: number }>();
  private static redis: Redis | null = null;

  // 初始化Redis连接
  static async init() {
    if (process.env.REDIS_URL) {
      this.redis = new Redis(process.env.REDIS_URL);
    }
  }

  // 获取缓存
  static async get<T>(key: string): Promise<T | null> {
    // 1. 检查内存缓存
    const memoryItem = this.memoryCache.get(key);
    if (memoryItem && Date.now() < memoryItem.expires) {
      return memoryItem.data;
    }

    // 2. 检查Redis缓存
    if (this.redis) {
      try {
        const redisData = await this.redis.get(key);
        if (redisData) {
          const parsed = JSON.parse(redisData);
          // 回填内存缓存
          this.memoryCache.set(key, {
            data: parsed,
            expires: Date.now() + 5 * 60 * 1000, // 5分钟
          });
          return parsed;
        }
      } catch (error) {
        console.error('Redis get error:', error);
      }
    }

    return null;
  }

  // 设置缓存
  static async set<T>(
    key: string,
    data: T,
    ttlSeconds = 300
  ): Promise<void> {
    const expires = Date.now() + ttlSeconds * 1000;

    // 1. 设置内存缓存
    this.memoryCache.set(key, { data, expires });

    // 2. 设置Redis缓存
    if (this.redis) {
      try {
        await this.redis.setex(key, ttlSeconds, JSON.stringify(data));
      } catch (error) {
        console.error('Redis set error:', error);
      }
    }
  }

  // 删除缓存
  static async delete(key: string): Promise<void> {
    this.memoryCache.delete(key);

    if (this.redis) {
      try {
        await this.redis.del(key);
      } catch (error) {
        console.error('Redis delete error:', error);
      }
    }
  }

  // 批量删除（模式匹配）
  static async deletePattern(pattern: string): Promise<void> {
    // 清理内存缓存
    for (const key of this.memoryCache.keys()) {
      if (key.includes(pattern)) {
        this.memoryCache.delete(key);
      }
    }

    // 清理Redis缓存
    if (this.redis) {
      try {
        const keys = await this.redis.keys(`*${pattern}*`);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      } catch (error) {
        console.error('Redis delete pattern error:', error);
      }
    }
  }
}

// 缓存装饰器
export function cached(ttlSeconds = 300) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${target.constructor.name}:${propertyName}:${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = await CacheManager.get(cacheKey);
      if (cached !== null) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);

      // 存入缓存
      await CacheManager.set(cacheKey, result, ttlSeconds);

      return result;
    };
  };
}

// 使用示例
export class ItemService {
  @cached(600) // 10分钟缓存
  static async getPopularItems(): Promise<Item[]> {
    return await sanityFetch<Item[]>({
      query: `*[_type == "item" && featured == true] | order(_createdAt desc) [0...10]`,
    });
  }

  @cached(300) // 5分钟缓存
  static async getItemsByCategory(categoryId: string): Promise<Item[]> {
    return await sanityFetch<Item[]>({
      query: `*[_type == "item" && references($categoryId)]`,
      params: { categoryId },
    });
  }
}
```

### 5.4 错误处理和日志规范

#### 5.4.1 统一错误处理
```typescript
// 标准错误响应格式
interface ApiErrorResponse {
  status: 'error';
  message: string;
  code?: string;
  details?: any;
  timestamp: string;
}

// 成功响应格式
interface ApiSuccessResponse<T = any> {
  status: 'success';
  data?: T;
  message?: string;
  timestamp: string;
}

// 错误处理中间件
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<ApiErrorResponse | R> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API Error:', error);

      // 根据错误类型返回不同响应
      if (error instanceof ValidationError) {
        return {
          status: 'error',
          message: '输入数据验证失败',
          code: 'VALIDATION_ERROR',
          details: error.details,
          timestamp: new Date().toISOString(),
        };
      }

      if (error instanceof AuthenticationError) {
        return {
          status: 'error',
          message: '身份验证失败',
          code: 'AUTH_ERROR',
          timestamp: new Date().toISOString(),
        };
      }

      // 默认服务器错误
      return {
        status: 'error',
        message: '服务器内部错误',
        code: 'INTERNAL_ERROR',
        timestamp: new Date().toISOString(),
      };
    }
  };
}
```

#### 5.4.2 日志记录策略
```typescript
// 日志级别定义
enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

// 日志记录函数
export function log(level: LogLevel, message: string, meta?: any) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level,
    message,
    meta,
    environment: process.env.NODE_ENV,
  };

  // 开发环境直接输出到控制台
  if (process.env.NODE_ENV === 'development') {
    console.log(JSON.stringify(logEntry, null, 2));
    return;
  }

  // 生产环境发送到日志服务
  // 可以集成 Winston, Pino 或云日志服务
}

// 关键操作日志记录
export function logUserAction(
  userId: string,
  action: string,
  details?: any
) {
  log(LogLevel.INFO, `User action: ${action}`, {
    userId,
    action,
    details,
  });
}
```

### 5.5 测试策略

#### 5.5.1 单元测试
```typescript
// 使用 Jest + Testing Library
// 测试覆盖率目标: > 80%

// 示例: 用户注册功能测试
describe('User Registration', () => {
  it('should register user with valid data', async () => {
    const userData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    };

    const result = await registerAction(userData);

    expect(result.status).toBe('success');
    expect(result.message).toContain('注册成功');
  });

  it('should reject invalid email format', async () => {
    const userData = {
      name: 'Test User',
      email: 'invalid-email',
      password: 'password123',
    };

    const result = await registerAction(userData);

    expect(result.status).toBe('error');
    expect(result.message).toContain('邮箱格式');
  });
});
```

#### 5.5.2 集成测试
```typescript
// API 端点测试
describe('API Integration Tests', () => {
  beforeEach(async () => {
    // 清理测试数据库
    await cleanupTestDatabase();
  });

  it('should handle complete item submission flow', async () => {
    // 1. 创建测试用户
    const user = await createTestUser();

    // 2. 上传图片
    const imageResponse = await uploadTestImage();

    // 3. 提交项目
    const submitResponse = await submitItem({
      name: 'Test Item',
      description: 'Test Description',
      imageId: imageResponse.asset._id,
      // ... 其他字段
    });

    expect(submitResponse.status).toBe('success');
  });
});
```

#### 5.5.3 端到端测试
```typescript
// 使用 Playwright 进行 E2E 测试
import { test, expect } from '@playwright/test';

test('user can submit and pay for item', async ({ page }) => {
  // 1. 用户注册和登录
  await page.goto('/auth/register');
  await page.fill('[name="email"]', '<EMAIL>');
  await page.fill('[name="password"]', 'password123');
  await page.click('button[type="submit"]');

  // 2. 提交项目
  await page.goto('/submit');
  await page.fill('[name="name"]', 'Test Product');
  await page.fill('[name="description"]', 'Test Description');
  // ... 填写其他字段

  // 3. 选择付费计划
  await page.goto('/payment/item-id');
  await page.click('[data-testid="pro-plan-button"]');

  // 4. 验证跳转到 Stripe
  await expect(page).toHaveURL(/checkout\.stripe\.com/);
});
```

### 5.6 SEO 优化实现

#### 5.6.1 技术 SEO
```typescript
// 动态元数据生成
export async function generateMetadata({ params }: {
  params: { slug: string }
}): Promise<Metadata> {
  const item = await getItemBySlug(params.slug);

  if (!item) {
    return {
      title: '页面未找到',
      description: '您访问的页面不存在',
    };
  }

  return {
    title: `${item.name} - ${siteConfig.name}`,
    description: item.description,
    keywords: [
      item.name,
      ...item.categories.map(c => c.name),
      ...item.tags.map(t => t.name),
    ],
    openGraph: {
      title: item.name,
      description: item.description,
      images: [
        {
          url: item.image.url,
          width: 1200,
          height: 630,
          alt: item.image.alt,
        },
      ],
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: item.name,
      description: item.description,
      images: [item.image.url],
    },
  };
}

// 结构化数据生成
export function generateStructuredData(item: Item) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: item.name,
    description: item.description,
    image: item.image.url,
    url: `${siteConfig.url}/item/${item.slug.current}`,
    brand: {
      '@type': 'Brand',
      name: siteConfig.name,
    },
    offers: {
      '@type': 'Offer',
      price: item.pricePlan === 'PRO' ? '9.90' : '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
    },
  };
}
```

#### 5.6.2 站点地图生成
```typescript
// app/sitemap.ts
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL!;

  // 获取所有已发布的项目
  const items = await sanityFetch<Item[]>({
    query: `*[
      _type == "item" &&
      (freePlanStatus == "approved" || proPlanStatus == "success")
    ] {
      slug,
      _updatedAt
    }`,
  });

  // 获取所有分类
  const categories = await sanityFetch<Category[]>({
    query: `*[_type == "category"] { slug, _updatedAt }`,
  });

  return [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8,
    },
    ...items.map((item) => ({
      url: `${baseUrl}/item/${item.slug.current}`,
      lastModified: new Date(item._updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    })),
    ...categories.map((category) => ({
      url: `${baseUrl}/category/${category.slug.current}`,
      lastModified: new Date(category._updatedAt),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    })),
  ];
}
```

### 5.7 安全性实现

#### 5.7.1 输入验证和清理
```typescript
// 使用 Zod 进行严格的输入验证
import { z } from 'zod';

// XSS 防护 - HTML 清理
import DOMPurify from 'isomorphic-dompurify';

export function sanitizeHtml(html: string): string {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: [],
  });
}

// SQL 注入防护 - 参数化查询
export function buildSafeQuery(baseQuery: string, params: Record<string, any>) {
  // Sanity GROQ 查询参数化
  return {
    query: baseQuery,
    params: Object.fromEntries(
      Object.entries(params).map(([key, value]) => [
        key,
        typeof value === 'string' ? value.replace(/['"]/g, '') : value
      ])
    ),
  };
}
```

#### 5.7.2 认证和授权
```typescript
// 权限检查中间件
export async function requireAuth(req: NextRequest) {
  const session = await getServerSession(authConfig);

  if (!session?.user) {
    throw new AuthenticationError('未登录');
  }

  return session.user;
}

export async function requireAdmin(req: NextRequest) {
  const user = await requireAuth(req);

  if (user.role !== 'ADMIN') {
    throw new AuthorizationError('权限不足');
  }

  return user;
}

// 资源所有权验证
export async function requireOwnership(userId: string, itemId: string) {
  const item = await getItemById(itemId);

  if (!item || item.submitter._id !== userId) {
    throw new AuthorizationError('无权访问此资源');
  }

  return item;
}
```

#### 5.7.3 速率限制
```typescript
// 使用 Redis 实现速率限制
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export async function rateLimit(
  identifier: string,
  limit: number,
  window: number // 时间窗口（秒）
): Promise<boolean> {
  const key = `rate_limit:${identifier}`;
  const current = await redis.incr(key);

  if (current === 1) {
    await redis.expire(key, window);
  }

  return current <= limit;
}

// API 路由中使用
export async function POST(req: NextRequest) {
  const ip = req.ip || 'unknown';

  // 每分钟最多 10 次请求
  const allowed = await rateLimit(ip, 10, 60);

  if (!allowed) {
    return NextResponse.json(
      { error: '请求过于频繁，请稍后再试' },
      { status: 429 }
    );
  }

  // 处理请求...
}
```

### 5.8 监控和分析

#### 5.8.1 性能监控
```typescript
// 使用 Vercel Analytics 和自定义指标
import { track } from '@vercel/analytics';

// 关键业务指标追踪
export function trackUserAction(action: string, properties?: Record<string, any>) {
  track(action, {
    timestamp: Date.now(),
    ...properties,
  });
}

// 性能指标监控
export function measurePerformance<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    const start = performance.now();

    try {
      const result = await fn();
      const duration = performance.now() - start;

      // 记录性能指标
      track('performance', {
        operation,
        duration,
        success: true,
      });

      resolve(result);
    } catch (error) {
      const duration = performance.now() - start;

      track('performance', {
        operation,
        duration,
        success: false,
        error: error.message,
      });

      reject(error);
    }
  });
}
```

#### 5.8.2 错误监控
```typescript
// 集成 Sentry 进行错误追踪
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
});

// 自定义错误报告
export function reportError(error: Error, context?: Record<string, any>) {
  Sentry.withScope((scope) => {
    if (context) {
      scope.setContext('additional_info', context);
    }
    Sentry.captureException(error);
  });
}

// 用户反馈收集
export function captureUserFeedback(
  userId: string,
  feedback: string,
  email?: string
) {
  Sentry.captureUserFeedback({
    event_id: Sentry.lastEventId(),
    name: userId,
    email: email || 'unknown',
    comments: feedback,
  });
}
```

#### 5.8.3 业务指标监控
```typescript
// 关键业务指标定义
interface BusinessMetrics {
  // 用户指标
  totalUsers: number;
  activeUsers: number;
  newRegistrations: number;

  // 内容指标
  totalItems: number;
  pendingItems: number;
  approvedItems: number;

  // 收入指标
  totalRevenue: number;
  conversionRate: number;
  averageOrderValue: number;
}

// 指标收集函数
export async function collectBusinessMetrics(): Promise<BusinessMetrics> {
  const [
    totalUsers,
    activeUsers,
    newRegistrations,
    totalItems,
    pendingItems,
    approvedItems,
    orders
  ] = await Promise.all([
    getUserCount(),
    getActiveUserCount(),
    getNewRegistrationCount(),
    getItemCount(),
    getPendingItemCount(),
    getApprovedItemCount(),
    getOrderStats(),
  ]);

  return {
    totalUsers,
    activeUsers,
    newRegistrations,
    totalItems,
    pendingItems,
    approvedItems,
    totalRevenue: orders.totalRevenue,
    conversionRate: orders.conversionRate,
    averageOrderValue: orders.averageOrderValue,
  };
}

// 定时任务收集指标
export async function scheduleMetricsCollection() {
  // 每小时收集一次指标
  setInterval(async () => {
    try {
      const metrics = await collectBusinessMetrics();

      // 发送到分析服务
      await sendMetricsToAnalytics(metrics);

      // 检查异常指标并告警
      await checkMetricAlerts(metrics);

    } catch (error) {
      reportError(error, { context: 'metrics_collection' });
    }
  }, 60 * 60 * 1000); // 1小时
}
```

---

## 6. 项目管理

### 6.1 开发里程碑

#### 6.1.1 MVP 阶段 (4周)
**目标**: 核心功能可用，支持基本的目录提交和展示

**功能范围**:
- 用户注册登录
- 项目提交和展示
- 基础分类和搜索
- 管理员审核功能

**验收标准**:
- 用户可以成功注册并提交项目
- 管理员可以审核和发布项目
- 基础搜索和筛选功能正常
- 响应式设计在主流设备上正常显示

#### 6.1.2 Beta 阶段 (6周)
**目标**: 完整功能实现，支持商业化运营

**功能范围**:
- 支付系统集成
- 邮件通知系统
- 博客功能
- SEO 优化
- 性能优化

**验收标准**:
- 支付流程完整可用
- 邮件发送成功率 > 95%
- 页面加载时间 < 3秒
- SEO 基础设施完整

#### 6.1.3 正式版 (8周)
**目标**: 生产就绪，支持大规模用户使用

**功能范围**:
- 高级搜索和推荐
- 数据分析和统计
- 多语言支持
- 高级管理功能

### 6.2 质量保证

#### 6.2.1 测试策略
- **单元测试**: 核心业务逻辑测试覆盖率 > 80%
- **集成测试**: API 接口和数据库交互测试
- **端到端测试**: 关键用户流程自动化测试
- **性能测试**: 负载测试和压力测试

#### 6.2.2 代码质量
- **代码规范**: ESLint + Prettier 自动格式化
- **类型检查**: TypeScript 严格模式
- **代码审查**: Pull Request 必须经过审查
- **自动化部署**: CI/CD 流水线自动部署

### 6.3 风险管理

#### 6.3.1 技术风险
- **第三方服务依赖**: Stripe, Resend, Sanity 服务可用性
- **性能瓶颈**: 大量用户并发访问
- **安全漏洞**: 用户数据和支付信息安全

**缓解措施**:
- 服务监控和备用方案
- 性能测试和优化
- 安全审计和渗透测试

#### 6.3.2 业务风险
- **用户接受度**: 产品市场适配度
- **竞争压力**: 同类产品竞争
- **法律合规**: 数据保护和支付合规

**缓解措施**:
- 用户反馈收集和快速迭代
- 差异化功能开发
- 法律咨询和合规审查

---

## 7. 成功指标

### 7.1 产品指标
- **用户增长**: 月活跃用户数 (MAU)
- **内容质量**: 项目提交数和通过率
- **用户参与**: 平均会话时长和页面浏览量
- **转化率**: 免费用户到付费用户转化率

### 7.2 技术指标
- **性能指标**: 页面加载时间、API 响应时间
- **可用性指标**: 系统正常运行时间 > 99.9%
- **安全指标**: 安全事件数量和响应时间

### 7.3 商业指标
- **收入指标**: 月度经常性收入 (MRR)
- **成本指标**: 获客成本 (CAC) 和客户生命周期价值 (LTV)
- **运营指标**: 客户支持响应时间和满意度

---

## 8. 附录

### 8.1 常用命令速查
```bash
# 开发环境
pnpm dev              # 启动开发服务器
pnpm build            # 构建生产版本
pnpm start            # 启动生产服务器
pnpm lint             # 代码检查
pnpm typegen          # 生成 Sanity 类型
pnpm email            # 启动邮件预览

# 部署相关
vercel                # 部署到 Vercel
docker build -t app . # 构建 Docker 镜像
docker run -p 3000:3000 app # 运行容器

# Sanity 相关
npx sanity login      # 登录 Sanity
npx sanity deploy     # 部署 Schema
npx sanity start      # 启动 Studio
```

### 8.2 重要链接
- **项目仓库**: https://github.com/your-org/mkdirs-template
- **演示站点**: https://demo.mkdirs.com
- **Sanity Studio**: https://your-domain.com/studio
- **Stripe Dashboard**: https://dashboard.stripe.com
- **Vercel Dashboard**: https://vercel.com/dashboard

### 8.3 数据备份和恢复策略

#### 8.3.1 Sanity 数据备份
```bash
# 导出数据备份
npx sanity dataset export production backup-$(date +%Y%m%d).tar.gz

# 定时备份脚本
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/sanity"
BACKUP_FILE="$BACKUP_DIR/backup_$DATE.tar.gz"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 导出数据
npx sanity dataset export production $BACKUP_FILE

# 上传到云存储 (AWS S3)
aws s3 cp $BACKUP_FILE s3://your-backup-bucket/sanity/

# 清理本地文件 (保留最近7天)
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE"
```

#### 8.3.2 数据恢复流程
```bash
# 从备份恢复数据
npx sanity dataset import backup-20250129.tar.gz production --replace

# 部分数据恢复
npx sanity dataset import backup-20250129.tar.gz production --missing
```

#### 8.3.3 灾难恢复计划
```typescript
// 灾难恢复检查清单
const disasterRecoveryChecklist = {
  immediate: [
    '评估影响范围和严重程度',
    '启动应急响应团队',
    '通知相关利益相关者',
    '切换到备用系统（如有）',
  ],
  shortTerm: [
    '从最新备份恢复数据',
    '验证数据完整性',
    '测试关键功能',
    '逐步恢复服务',
  ],
  longTerm: [
    '分析故障原因',
    '更新备份策略',
    '改进监控系统',
    '制定预防措施',
  ],
};

// 自动化健康检查
export async function performHealthCheck(): Promise<HealthCheckResult> {
  const checks = await Promise.allSettled([
    checkDatabaseConnection(),
    checkExternalServices(),
    checkCriticalEndpoints(),
    checkDiskSpace(),
    checkMemoryUsage(),
  ]);

  const results = checks.map((check, index) => ({
    name: ['Database', 'External Services', 'API Endpoints', 'Disk Space', 'Memory'][index],
    status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
    details: check.status === 'fulfilled' ? check.value : check.reason,
  }));

  const overallHealth = results.every(r => r.status === 'healthy') ? 'healthy' : 'unhealthy';

  return {
    overall: overallHealth,
    timestamp: new Date().toISOString(),
    checks: results,
  };
}
```

### 8.4 技术支持
- **文档问题**: 联系产品团队
- **技术问题**: 联系开发团队
- **部署问题**: 联系运维团队
- **紧急问题**: 24/7 技术支持热线

### 8.5 更新日志
- **v2.0** (2025-01-29): 完整技术实现文档，包含所有功能模块的详细代码示例
- **v1.0** (2025-01-29): 初始版本，基础功能清单

---

*文档版本: v2.0*
*最后更新: 2025-01-29*
*负责人: 产品团队*
*审核人: 技术负责人*
*技术实现: 开发团队*
