import { and, desc, eq, ilike, inArray, ne, sql, or } from "drizzle-orm";
import { getDb } from "../index";
import { items, categories, tags, itemCategories, itemTags, user } from "../schema";

export type ItemWithRelations = typeof items.$inferSelect & {
  categories: (typeof categories.$inferSelect)[];
  tags: (typeof tags.$inferSelect)[];
  submitter: {
    id: string;
    name: string;
    image: string | null;
  };
};

/**
 * 获取所有已发布的项目（分页）
 */
export async function getPublishedItems(params: {
  page?: number;
  limit?: number;
  categoryId?: string;
  tagId?: string;
  search?: string;
  featured?: boolean;
} = {}) {
  const db = await getDb();
  const { page = 1, limit = 20, categoryId, tagId, search, featured } = params;

  // 构建基础查询条件
  const whereConditions = [
    or(
      eq(items.freePlanStatus, 'approved'),
      eq(items.proPlanStatus, 'success')
    )
  ];

  // 分类筛选
  if (categoryId) {
    whereConditions.push(eq(categories.id, categoryId));
  }

  // 标签筛选
  if (tagId) {
    whereConditions.push(eq(tags.id, tagId));
  }

  // 搜索条件
  if (search) {
    whereConditions.push(
      or(
        ilike(items.name, `%${search}%`),
        ilike(items.description, `%${search}%`)
      )!
    );
  }

  // 精选筛选
  if (featured !== undefined) {
    whereConditions.push(eq(items.featured, featured));
  }

  // 获取数据
  const results = await db
    .select({
      items: items,
      categories: categories,
      tags: tags,
      submitter: {
        id: user.id,
        name: user.name,
        image: user.image,
      }
    })
    .from(items)
    .leftJoin(itemCategories, eq(items.id, itemCategories.itemId))
    .leftJoin(categories, eq(itemCategories.categoryId, categories.id))
    .leftJoin(itemTags, eq(items.id, itemTags.itemId))
    .leftJoin(tags, eq(itemTags.tagId, tags.id))
    .leftJoin(user, eq(items.submitterId, user.id))
    .where(and(...whereConditions))
    .orderBy(desc(items.createdAt))
    .limit(limit)
    .offset((page - 1) * limit);

  // 组织数据结构
  const itemsMap = new Map<string, ItemWithRelations>();

  for (const row of results) {
    const itemId = row.items.id;

    if (!itemsMap.has(itemId)) {
      itemsMap.set(itemId, {
        ...row.items,
        categories: [],
        tags: [],
        submitter: row.submitter!
      });
    }

    const item = itemsMap.get(itemId)!;

    // 添加分类（去重）
    if (row.categories && !item.categories.find(c => c.id === row.categories!.id)) {
      item.categories.push(row.categories);
    }

    // 添加标签（去重）
    if (row.tags && !item.tags.find(t => t.id === row.tags!.id)) {
      item.tags.push(row.tags);
    }
  }

  return Array.from(itemsMap.values());
}

/**
 * 根据slug获取项目详情
 */
export async function getItemBySlug(slug: string) {
  const db = await getDb();

  const results = await db
    .select({
      items: items,
      categories: categories,
      tags: tags,
      submitter: {
        id: user.id,
        name: user.name,
        image: user.image,
      }
    })
    .from(items)
    .leftJoin(itemCategories, eq(items.id, itemCategories.itemId))
    .leftJoin(categories, eq(itemCategories.categoryId, categories.id))
    .leftJoin(itemTags, eq(items.id, itemTags.itemId))
    .leftJoin(tags, eq(itemTags.tagId, tags.id))
    .leftJoin(user, eq(items.submitterId, user.id))
    .where(eq(items.slug, slug));

  if (results.length === 0) {
    return null;
  }

  // 组织数据
  const firstRow = results[0];
  const item: ItemWithRelations = {
    ...firstRow.items,
    categories: results
      .filter(r => r.categories !== null)
      .map(r => r.categories!)
      .filter((c, index, self) => self.findIndex(item => item.id === c.id) === index),
    tags: results
      .filter(r => r.tags !== null)
      .map(r => r.tags!)
      .filter((t, index, self) => self.findIndex(item => item.id === t.id) === index),
    submitter: firstRow.submitter!
  };

  return item;
}

/**
 * 创建新项目
 */
export async function createItem(data: typeof items.$inferInsert) {
  const db = await getDb();
  const [newItem] = await db.insert(items).values(data).returning();
  return newItem;
}

/**
 * 为项目添加分类关联
 */
export async function addItemCategories(itemId: string, categoryIds: string[]) {
  if (categoryIds.length === 0) return;

  const db = await getDb();
  const relations = categoryIds.map(categoryId => ({
    itemId,
    categoryId
  }));

  await db.insert(itemCategories).values(relations);
}

/**
 * 为项目添加标签关联
 */
export async function addItemTags(itemId: string, tagIds: string[]) {
  if (tagIds.length === 0) return;

  const db = await getDb();
  const relations = tagIds.map(tagId => ({
    itemId,
    tagId
  }));

  await db.insert(itemTags).values(relations);
}

/**
 * 获取用户提交的项目
 */
export async function getUserItems(userId: string) {
  const db = await getDb();

  return await db
    .select()
    .from(items)
    .where(eq(items.submitterId, userId))
    .orderBy(desc(items.createdAt));
}

/**
 * 获取精选项目
 */
export async function getFeaturedItems(limit = 6) {
  const db = await getDb();

  const results = await db
    .select({
      items: items,
      categories: categories,
      tags: tags,
      submitter: {
        id: user.id,
        name: user.name,
        image: user.image,
      }
    })
    .from(items)
    .leftJoin(itemCategories, eq(items.id, itemCategories.itemId))
    .leftJoin(categories, eq(itemCategories.categoryId, categories.id))
    .leftJoin(itemTags, eq(items.id, itemTags.itemId))
    .leftJoin(tags, eq(itemTags.tagId, tags.id))
    .leftJoin(user, eq(items.submitterId, user.id))
    .where(and(
      eq(items.featured, true),
      eq(items.freePlanStatus, 'approved')
    ))
    .orderBy(desc(items.createdAt))
    .limit(limit);

  // 组织数据结构
  const itemsMap = new Map<string, ItemWithRelations>();

  for (const row of results) {
    const itemId = row.items.id;

    if (!itemsMap.has(itemId)) {
      itemsMap.set(itemId, {
        ...row.items,
        categories: [],
        tags: [],
        submitter: row.submitter!
      });
    }

    const item = itemsMap.get(itemId)!;

    // 添加分类（去重）
    if (row.categories && !item.categories.find(c => c.id === row.categories!.id)) {
      item.categories.push(row.categories);
    }

    // 添加标签（去重）
    if (row.tags && !item.tags.find(t => t.id === row.tags!.id)) {
      item.tags.push(row.tags);
    }
  }

  return Array.from(itemsMap.values());
}

/**
 * 获取相关项目
 */
export async function getRelatedItems(
  itemId: string,
  options: {
    categoryIds?: string[];
    tagIds?: string[];
    limit?: number;
  } = {}
) {
  const { categoryIds = [], tagIds = [], limit = 6 } = options;
  const db = await getDb();

  const results = await db
    .select({
      items: items,
      categories: categories,
      tags: tags,
    })
    .from(items)
    .leftJoin(itemCategories, eq(items.id, itemCategories.itemId))
    .leftJoin(categories, eq(itemCategories.categoryId, categories.id))
    .leftJoin(itemTags, eq(items.id, itemTags.itemId))
    .leftJoin(tags, eq(itemTags.tagId, tags.id))
    .where(
      and(
        ne(items.id, itemId),
        or(
          eq(items.freePlanStatus, 'approved'),
          eq(items.proPlanStatus, 'success')
        ),
        categoryIds.length > 0
          ? inArray(categories.id, categoryIds)
          : sql`true`
      )
    )
    .orderBy(desc(items.featured), desc(items.createdAt))
    .limit(limit * 3); // 获取更多数据以便去重后仍有足够的项目

  // 组织数据，去重并限制数量
  const itemsMap = new Map<string, ItemWithRelations>();

  for (const row of results) {
    const itemId = row.items.id;

    if (!itemsMap.has(itemId)) {
      itemsMap.set(itemId, {
        ...row.items,
        categories: [],
        tags: [],
        submitter: { id: '', name: '', image: null }
      });
    }

    const item = itemsMap.get(itemId)!;

    if (row.categories && !item.categories.some(c => c.id === row.categories!.id)) {
      item.categories.push(row.categories);
    }

    if (row.tags && !item.tags.some(t => t.id === row.tags!.id)) {
      item.tags.push(row.tags);
    }
  }

  return Array.from(itemsMap.values()).slice(0, limit);
}
