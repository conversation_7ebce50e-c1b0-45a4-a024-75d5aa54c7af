import type { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import { 
  user, 
  session, 
  account, 
  verification, 
  payment, 
  userCredit, 
  creditTransaction,
  categories,
  tags,
  items,
  itemCategories,
  itemTags
} from './schema';

// ============= 原有类型 =============
export type User = InferSelectModel<typeof user>;
export type NewUser = InferInsertModel<typeof user>;

export type Session = InferSelectModel<typeof session>;
export type NewSession = InferInsertModel<typeof session>;

export type Account = InferSelectModel<typeof account>;
export type NewAccount = InferInsertModel<typeof account>;

export type Verification = InferSelectModel<typeof verification>;
export type NewVerification = InferInsertModel<typeof verification>;

export type Payment = InferSelectModel<typeof payment>;
export type NewPayment = InferInsertModel<typeof payment>;

export type UserCredit = InferSelectModel<typeof userCredit>;
export type NewUserCredit = InferInsertModel<typeof userCredit>;

export type CreditTransaction = InferSelectModel<typeof creditTransaction>;
export type NewCreditTransaction = InferInsertModel<typeof creditTransaction>;

// ============= 导航站类型 =============
export type Category = InferSelectModel<typeof categories>;
export type NewCategory = InferInsertModel<typeof categories>;

export type Tag = InferSelectModel<typeof tags>;
export type NewTag = InferInsertModel<typeof tags>;

export type Item = InferSelectModel<typeof items>;
export type NewItem = InferInsertModel<typeof items>;

export type ItemCategory = InferSelectModel<typeof itemCategories>;
export type NewItemCategory = InferInsertModel<typeof itemCategories>;

export type ItemTag = InferSelectModel<typeof itemTags>;
export type NewItemTag = InferInsertModel<typeof itemTags>;

// ============= 复合类型 =============
export type ItemWithDetails = Item & {
  submitter: User;
  categories: Category[];
  tags: Tag[];
  creditTransaction?: CreditTransaction;
};

export type CategoryWithCount = Category & {
  itemCount: number;
};

export type TagWithCount = Tag & {
  itemCount: number;
};

// ============= 表单类型 =============
export interface ItemSubmissionData {
  name: string;
  description: string;
  introduction?: string;
  link: string;
  imageUrl?: string;
  categories: string[]; // category IDs
  tags: string[]; // tag IDs
  servicePlan: 'basic' | 'featured' | 'top';
}

// ============= 积分相关类型 =============
export interface SubmissionCreditCost {
  basic: number;
  featured: number;
  top: number;
}

export interface CreditPackage {
  credits: number;
  price: number;
  bonus: number;
  description: string;
}

// ============= 搜索和筛选类型 =============
export interface SearchParams {
  query?: string;
  category?: string;
  tag?: string;
  sort?: 'newest' | 'oldest' | 'popular' | 'random';
  featured?: boolean;
  page?: number;
  limit?: number;
}

export interface SearchResult {
  items: ItemWithDetails[];
  totalCount: number;
  page: number;
  totalPages: number;
}
