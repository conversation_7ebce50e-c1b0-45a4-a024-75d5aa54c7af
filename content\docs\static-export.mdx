---
title: Static Export
description: Enable static export with Fumadocs
---

## Overview

Fumadocs is fully compatible with Next.js static export, allowing you to export the app as a static HTML site without a Node.js server.

```js title="next.config.mjs"
/**
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  output: 'export',
};
```

## Search

### Cloud Solutions

Since the search functionality is powered by remote servers, static export works without configuration.

### Built-in Search

The default search config of Orama Search uses route handlers, which is not supported by static export.

Instead, you can build the search indexes statically following the [Orama Search](/docs/headless/search/orama#static-export) guide.
And enable static mode on search client from Root Provider:

```tsx title="app/layout.tsx"
import { RootProvider } from 'fumadocs-ui/provider';
import type { ReactNode } from 'react';

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <RootProvider
          search={{
            options: {
              type: 'static', // [!code highlight]
            },
          }}
        >
          {children}
        </RootProvider>
      </body>
    </html>
  );
}
```

This allows the route handler to be statically cached into a single file, and search will be computed on browser instead.
