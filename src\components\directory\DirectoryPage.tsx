import { items, categories } from "@/db/schema";
import type { InferSelectModel } from "drizzle-orm";
import CategoryFilter from "./CategoryFilter";
import ItemGrid from "./ItemGrid";

type Item = InferSelectModel<typeof items>;
type Category = InferSelectModel<typeof categories>;

interface Props {
  items: Item[];
  categories: Category[];
}

export default function DirectoryPage({ items, categories }: Props) {
  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">工具导航目录</h1>

      <div className="grid gap-8">
        <CategoryFilter categories={categories} />
        <ItemGrid items={items} />
      </div>
    </div>
  );
}
