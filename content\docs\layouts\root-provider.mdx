---
title: Root Provider
description: The context provider of Fumadocs UI.
---

The context provider of all the components, including `next-themes` and context
for search dialog. It should be located at the root layout.

## Usage

```jsx
import { RootProvider } from 'fumadocs-ui/provider';

export default function Layout({ children }) {
  return (
    <html lang="en">
      <body>
        <RootProvider>{children}</RootProvider>
      </body>
    </html>
  );
}
```

### Search Dialog

Customize or disable the search dialog with `search` option.

```jsx
<RootProvider
  search={{
    enabled: false,
  }}
>
  {children}
</RootProvider>
```

Learn more from [Search](/docs/search).

### Theme Provider

Fumadocs supports light/dark modes with [`next-themes`](https://github.com/pacocoursey/next-themes).
Customise or disable it with `theme` option.

```jsx
<RootProvider
  theme={{
    enabled: false,
  }}
>
  {children}
</RootProvider>
```
