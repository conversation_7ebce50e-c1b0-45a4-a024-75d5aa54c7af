import { DashboardHeader } from '@/components/dashboard/dashboard-header';
import { getUserItems } from '@/db/queries/items';
import { auth } from '@/lib/auth';
import { useTranslations } from 'next-intl';
import { redirect } from 'next/navigation';

/**
 * Dashboard page for navigation site
 * Shows user's submitted items and management features
 */
export default async function DashboardPage() {
  const session = await auth();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  const userItems = await getUserItems(session.user.id);
  const t = useTranslations();

  const breadcrumbs = [
    {
      label: t('Dashboard.dashboard.title'),
      isCurrentPage: true,
    },
  ];

  return (
    <>
      <DashboardHeader breadcrumbs={breadcrumbs} />

      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <DashboardContent
              userItems={userItems}
              translations={{
                title: t('Dashboard.dashboard.title'),
                myItems: t('Dashboard.myItems'),
                submitNew: t('Dashboard.submitNew'),
                totalItems: t('Dashboard.totalItems'),
                pendingReview: t('Dashboard.pendingReview'),
                approved: t('Dashboard.approved'),
                rejected: t('Dashboard.rejected'),
                noItems: t('Dashboard.noItems'),
                noItemsDescription: t('Dashboard.noItemsDescription'),
                status: t('Dashboard.status'),
                actions: t('Dashboard.actions'),
                edit: t('Dashboard.edit'),
                delete: t('Dashboard.delete'),
                view: t('Dashboard.view'),
              }}
            />
          </div>
        </div>
      </div>
    </>
  );
}
