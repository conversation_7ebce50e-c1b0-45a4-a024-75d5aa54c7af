declare module './HomeContent' {
  import { ComponentType } from 'react';
  import type { InferSelectModel } from 'drizzle-orm';
  import { items, categories } from '@/db/schema';

  type Item = InferSelectModel<typeof items>;
  type Category = InferSelectModel<typeof categories>;

  interface Props {
    initialItems: Item[];
    categories: Category[];
    t: (key: string) => string;
    locale: string;
  }

  const HomeContent: ComponentType<Props>;
  export default HomeContent;
}
