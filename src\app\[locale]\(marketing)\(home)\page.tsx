import { getAllCategories } from '@/db/queries/categories';
import { getFeaturedItems } from '@/db/queries/items';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import HomeContent from './HomeContent';

/**
 * https://next-intl.dev/docs/environments/actions-metadata-route-handlers#metadata-api
 */
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'Metadata' });

  return constructMetadata({
    title: t('title'),
    description: t('description'),
    canonicalUrl: getUrlWithLocale('', locale),
  });
}

interface HomePageProps {
  params: Promise<{ locale: Locale }>;
}

export default async function HomePage(props: HomePageProps) {
  const params = await props.params;
  const { locale } = params;
  const t = await getTranslations('HomePage');
  const [featuredItems, categories] = await Promise.all([
    getFeaturedItems(),
    getAllCategories(),
  ]);

  return (
    <HomeContent
      locale={locale}
      initialItems={featuredItems}
      categories={categories}
      translations={{
        featuredCategories: t('featuredCategories'),
        featuredTools: t('featuredTools'),
        itemsInCategory: t('itemsInCategory'),
      }}
    />
  );
}
