---
title: Docs Page
description: A page in your documentation
---

Page is the base element of a documentation, it includes Table of contents,
Footer, and Breadcrumb.

## Usage

```tsx title="page.tsx"
import {
  DocsPage,
  DocsDescription,
  DocsTitle,
  DocsBody,
} from 'fumadocs-ui/page';

<DocsPage>
  <DocsTitle>title</DocsTitle>
  <DocsDescription>description</DocsDescription>
  <DocsBody>...</DocsBody>
</DocsPage>;
```

<Callout type='info' title='Good to know'>

Instead of rendering the title with `DocsTitle` in `page.tsx`, you can put the title into MDX file.
This will render the title in the MDX body.

</Callout>

### Body

It applies the [Typography](/docs/theme#typography) styles, wrap your content inside.

```tsx
import { DocsBody } from 'fumadocs-ui/page';

<DocsBody>
  <h1>This heading looks good!</h1>
</DocsBody>;
```

### Category

Optional, link the other pages in its (page tree) folder with cards.

> You can use this component without `<DocsPage />`.

```tsx title="page.tsx"
import { source } from '@/lib/source';
import { DocsCategory } from 'fumadocs-ui/page';

const page = source.getPage(['...']);

<DocsCategory page={page} from={source} />;
```

**Demo:**

{/* DocsCategory is not supported */}
{/* <DocsCategory /> */}

## Configurations

### Full Mode

To extend the page to fill up all available space, pass `full` to the page component.
This will force TOC to be shown as a popover.

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage full>...</DocsPage>;
```

### Table of Contents

An overview of all the headings in your article, it requires an array of headings.

For Markdown and MDX documents, You can obtain it using the
[TOC Utility](/docs/headless/utils/get-toc). Content sources like Fumadocs MDX offer this out-of-the-box.

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage toc={headings}>...</DocsPage>;
```

Customise or disable TOC from your documentation with the `tableOfContent` option.

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage tableOfContent={options}>...</DocsPage>;
```

{/* <AutoTypeTable path="./content/docs/props.ts" name="TOCProps" /> */}

#### Style

You can choose another style for TOC, like `clerk` inspired by https://clerk.com:

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage
  tableOfContent={{
    style: 'clerk',
  }}
>
  ...
</DocsPage>;
```

#### Popover Mode

On smaller devices, it is shown on a popover instead.
Customise it with the `tableOfContentPopover` option.

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage tableOfContentPopover={options}>...</DocsPage>;
```

{/* <AutoTypeTable path="./content/docs/props.ts" name="TOCPopoverProps" /> */}

### Last Updated Time

Display last updated time of the page.

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage lastUpdate={new Date(lastModifiedTime)} />;
```

Since you might have different version controls (e.g. Github) or it's from
remote sources like Sanity, Fumadocs UI doesn't display the last updated time by
default.

For Github hosted documents, you can use
the [`getGithubLastEdit`](/docs/headless/utils/git-last-edit) utility.

```tsx
import { DocsPage } from 'fumadocs-ui/page';
import { getGithubLastEdit } from 'fumadocs-core/server';

const time = await getGithubLastEdit({
  owner: 'fuma-nama',
  repo: 'fumadocs',
  path: `content/docs/${page.file.path}`,
});

<DocsPage lastUpdate={new Date(time)} />;
```

<Callout type='info' title='Note'>

    You can also specify the last updated time of documents (e.g. using frontmatter).
    Don't forget to [update the schema type](/docs/mdx/collections#schema) on Fumadocs MDX first.

</Callout>

### Edit on GitHub

Add "Edit on GitHub" button to the page.

```tsx
import { DocsPage } from 'fumadocs-ui/page';

<DocsPage
  editOnGithub={{
    owner: 'fuma-nama',
    repo: 'fumadocs',
    sha: 'main',
    // file path, make sure it's valid
    path: `content/docs/${page.file.path}`,
  }}
/>;
```

### Footer

Footer is a navigation element that has two buttons to jump to the next and previous pages. When not specified, it shows the neighbour pages found from page tree.

Customise the footer with the `footer` option.

```tsx
import { DocsPage, DocsBody } from 'fumadocs-ui/page';

<DocsPage footer={options}>
  <DocsBody>...</DocsBody>
</DocsPage>;
```

{/* <AutoTypeTable path="./content/docs/props.ts" name="FooterProps" /> */}

### Breadcrumb

A navigation element, shown only when user is navigating in folders.

{/* <AutoTypeTable path="./content/docs/props.ts" name="BreadcrumbProps" /> */}

### MDX Page

In conjunction of Fumadocs MDX, you may create a `page.mdx` file and add the following.

```mdx
export { withArticle as default } from 'fumadocs-ui/page';

## Hello World
```

This creates a page with MDX, with proper typography styles applied.
