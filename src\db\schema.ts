import { boolean, integer, pgTable, text, timestamp, index, varchar, primaryKey } from "drizzle-orm/pg-core";

export const user = pgTable("user", {
	id: text("id").primaryKey(),
	name: text('name').notNull(),
	email: text('email').notNull().unique(),
	emailVerified: boolean('email_verified').notNull(),
	image: text('image'),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull(),
	role: text('role'),
	banned: boolean('banned'),
	banReason: text('ban_reason'),
	banExpires: timestamp('ban_expires'),
	customerId: text('customer_id'),
}, (table) => ({
	userIdIdx: index("user_id_idx").on(table.id),
	userCustomerIdIdx: index("user_customer_id_idx").on(table.customerId),
	userRoleIdx: index("user_role_idx").on(table.role),
}));

export const session = pgTable("session", {
	id: text("id").primaryKey(),
	expiresAt: timestamp('expires_at').notNull(),
	token: text('token').notNull().unique(),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull(),
	ipAddress: text('ip_address'),
	userAgent: text('user_agent'),
	userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
	impersonatedBy: text('impersonated_by')
}, (table) => ({
	sessionTokenIdx: index("session_token_idx").on(table.token),
	sessionUserIdIdx: index("session_user_id_idx").on(table.userId),
}));

export const account = pgTable("account", {
	id: text("id").primaryKey(),
	accountId: text('account_id').notNull(),
	providerId: text('provider_id').notNull(),
	userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
	accessToken: text('access_token'),
	refreshToken: text('refresh_token'),
	idToken: text('id_token'),
	accessTokenExpiresAt: timestamp('access_token_expires_at'),
	refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
	scope: text('scope'),
	password: text('password'),
	createdAt: timestamp('created_at').notNull(),
	updatedAt: timestamp('updated_at').notNull()
}, (table) => ({
	accountUserIdIdx: index("account_user_id_idx").on(table.userId),
	accountAccountIdIdx: index("account_account_id_idx").on(table.accountId),
	accountProviderIdIdx: index("account_provider_id_idx").on(table.providerId),
}));

export const verification = pgTable("verification", {
	id: text("id").primaryKey(),
	identifier: text('identifier').notNull(),
	value: text('value').notNull(),
	expiresAt: timestamp('expires_at').notNull(),
	createdAt: timestamp('created_at'),
	updatedAt: timestamp('updated_at')
});

export const payment = pgTable("payment", {
	id: text("id").primaryKey(),
	priceId: text('price_id').notNull(),
	type: text('type').notNull(),
	interval: text('interval'),
	userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
	customerId: text('customer_id').notNull(),
	subscriptionId: text('subscription_id'),
	sessionId: text('session_id'),
	status: text('status').notNull(),
	periodStart: timestamp('period_start'),
	periodEnd: timestamp('period_end'),
	cancelAtPeriodEnd: boolean('cancel_at_period_end'),
	trialStart: timestamp('trial_start'),
	trialEnd: timestamp('trial_end'),
	createdAt: timestamp('created_at').notNull().defaultNow(),
	updatedAt: timestamp('updated_at').notNull().defaultNow(),
}, (table) => ({
	paymentTypeIdx: index("payment_type_idx").on(table.type),
	paymentPriceIdIdx: index("payment_price_id_idx").on(table.priceId),
	paymentUserIdIdx: index("payment_user_id_idx").on(table.userId),
	paymentCustomerIdIdx: index("payment_customer_id_idx").on(table.customerId),
	paymentStatusIdx: index("payment_status_idx").on(table.status),
	paymentSubscriptionIdIdx: index("payment_subscription_id_idx").on(table.subscriptionId),
	paymentSessionIdIdx: index("payment_session_id_idx").on(table.sessionId),
}));

export const userCredit = pgTable("user_credit", {
	id: text("id").primaryKey(),
	userId: text("user_id").notNull().references(() => user.id, { onDelete: 'cascade' }),
	currentCredits: integer("current_credits").notNull().default(0),
	lastRefreshAt: timestamp("last_refresh_at"),
	createdAt: timestamp("created_at").notNull().defaultNow(),
	updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => ({
	userCreditUserIdIdx: index("user_credit_user_id_idx").on(table.userId),
}));

export const creditTransaction = pgTable("credit_transaction", {
	id: text("id").primaryKey(),
	userId: text("user_id").notNull().references(() => user.id, { onDelete: 'cascade' }),
	type: text("type").notNull(),
	description: text("description"),
	amount: integer("amount").notNull(),
	remainingAmount: integer("remaining_amount"),
	paymentId: text("payment_id"),
	expirationDate: timestamp("expiration_date"),
	expirationDateProcessedAt: timestamp("expiration_date_processed_at"),
	createdAt: timestamp("created_at").notNull().defaultNow(),
	updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => ({
	creditTransactionUserIdIdx: index("credit_transaction_user_id_idx").on(table.userId),
	creditTransactionTypeIdx: index("credit_transaction_type_idx").on(table.type),
}));

// ============================================================================
// 导航站核心数据模型
// ============================================================================

// 项目/工具表
export const items = pgTable("items", {
	id: text("id").primaryKey(),
	name: varchar("name", { length: 100 }).notNull(),
	slug: varchar("slug", { length: 120 }).unique().notNull(),
	description: text("description").notNull(),
	introduction: text("introduction"), // 详细介绍
	link: varchar("link", { length: 500 }).notNull(),
	imageUrl: varchar("image_url", { length: 500 }),

	// 状态管理
	publishDate: timestamp("publish_date"),
	featured: boolean("featured").default(false),
	paid: boolean("paid").default(false),

	// 计划状态
	pricePlan: varchar("price_plan", { length: 20 }).default('FREE'),
	freePlanStatus: varchar("free_plan_status", { length: 20 }).default('submitting'),
	proPlanStatus: varchar("pro_plan_status", { length: 20 }),
	rejectionReason: text("rejection_reason"),

	// 积分消耗记录
	creditsUsed: integer("credits_used").default(0),
	creditTransactionId: text("credit_transaction_id").references(() => creditTransaction.id),
	
	// 关联用户
	submitterId: text("submitter_id").references(() => user.id).notNull(),

	// 时间戳
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
	itemsSlugIdx: index("items_slug_idx").on(table.slug),
	itemsSubmitterIdIdx: index("items_submitter_id_idx").on(table.submitterId),
	itemsFeaturedIdx: index("items_featured_idx").on(table.featured),
	itemsPaidIdx: index("items_paid_idx").on(table.paid),
	itemsCreatedAtIdx: index("items_created_at_idx").on(table.createdAt),
}));

// 分类表
export const categories = pgTable("categories", {
	id: text("id").primaryKey(),
	name: varchar("name", { length: 50 }).notNull(),
	slug: varchar("slug", { length: 60 }).unique().notNull(),
	description: text("description"),
	priority: integer("priority").default(0),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
	categoriesSlugIdx: index("categories_slug_idx").on(table.slug),
	categoriesPriorityIdx: index("categories_priority_idx").on(table.priority),
}));

// 标签表
export const tags = pgTable("tags", {
	id: text("id").primaryKey(),
	name: varchar("name", { length: 30 }).notNull().unique(),
	slug: varchar("slug", { length: 40 }).unique().notNull(),
	description: text("description"),
	color: varchar("color", { length: 7 }), // 十六进制颜色代码
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
	tagsSlugIdx: index("tags_slug_idx").on(table.slug),
	tagsNameIdx: index("tags_name_idx").on(table.name),
}));

// 项目-分类关系表（多对多）
export const itemCategories = pgTable("item_categories", {
	itemId: text("item_id").references(() => items.id, { onDelete: 'cascade' }).notNull(),
	categoryId: text("category_id").references(() => categories.id, { onDelete: 'cascade' }).notNull(),
}, (table) => ({
	pk: primaryKey({ columns: [table.itemId, table.categoryId] }),
	itemCategoriesItemIdIdx: index("item_categories_item_id_idx").on(table.itemId),
	itemCategoriesCategoryIdIdx: index("item_categories_category_id_idx").on(table.categoryId),
}));

// 项目-标签关系表（多对多）
export const itemTags = pgTable("item_tags", {
	itemId: text("item_id").references(() => items.id, { onDelete: 'cascade' }).notNull(),
	tagId: text("tag_id").references(() => tags.id, { onDelete: 'cascade' }).notNull(),
}, (table) => ({
	pk: primaryKey({ columns: [table.itemId, table.tagId] }),
	itemTagsItemIdIdx: index("item_tags_item_id_idx").on(table.itemId),
	itemTagsTagIdIdx: index("item_tags_tag_id_idx").on(table.tagId),
}));
