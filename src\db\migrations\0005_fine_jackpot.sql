ALTER TABLE "items" DROP CONSTRAINT "items_credit_transaction_id_credit_transaction_id_fk";
--> statement-breakpoint
ALTER TABLE "items" DROP CONSTRAINT "items_submitter_id_user_id_fk";
--> statement-breakpoint
DROP INDEX "item_category_item_id_idx";--> statement-breakpoint
DROP INDEX "item_category_category_id_idx";--> statement-breakpoint
DROP INDEX "item_tag_item_id_idx";--> statement-breakpoint
DROP INDEX "item_tag_tag_id_idx";--> statement-breakpoint
DROP INDEX "item_submitter_id_idx";--> statement-breakpoint
ALTER TABLE "categories" ALTER COLUMN "name" SET DATA TYPE varchar(50);--> statement-breakpoint
ALTER TABLE "categories" ALTER COLUMN "slug" SET DATA TYPE varchar(60);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "name" SET DATA TYPE varchar(100);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "slug" SET DATA TYPE varchar(120);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "link" SET DATA TYPE varchar(500);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "image_url" SET DATA TYPE varchar(500);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "price_plan" SET DATA TYPE varchar(20);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "free_plan_status" SET DATA TYPE varchar(20);--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "free_plan_status" SET DEFAULT 'published';--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "pro_plan_status" SET DATA TYPE varchar(20);--> statement-breakpoint
ALTER TABLE "tags" ALTER COLUMN "name" SET DATA TYPE varchar(30);--> statement-breakpoint
ALTER TABLE "tags" ALTER COLUMN "slug" SET DATA TYPE varchar(40);--> statement-breakpoint
ALTER TABLE "item_categories" ADD CONSTRAINT "item_categories_item_id_category_id_pk" PRIMARY KEY("item_id","category_id");--> statement-breakpoint
ALTER TABLE "item_tags" ADD CONSTRAINT "item_tags_item_id_tag_id_pk" PRIMARY KEY("item_id","tag_id");--> statement-breakpoint
ALTER TABLE "tags" ADD COLUMN "color" varchar(7);--> statement-breakpoint
ALTER TABLE "items" ADD CONSTRAINT "items_submitter_id_user_id_fk" FOREIGN KEY ("submitter_id") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "item_categories_item_idx" ON "item_categories" USING btree ("item_id");--> statement-breakpoint
CREATE INDEX "item_categories_category_idx" ON "item_categories" USING btree ("category_id");--> statement-breakpoint
CREATE INDEX "item_tags_item_idx" ON "item_tags" USING btree ("item_id");--> statement-breakpoint
CREATE INDEX "item_tags_tag_idx" ON "item_tags" USING btree ("tag_id");--> statement-breakpoint
CREATE INDEX "item_submitter_idx" ON "items" USING btree ("submitter_id");--> statement-breakpoint
CREATE INDEX "item_paid_idx" ON "items" USING btree ("paid");--> statement-breakpoint
CREATE INDEX "item_created_at_idx" ON "items" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "tag_name_idx" ON "tags" USING btree ("name");--> statement-breakpoint
ALTER TABLE "items" DROP COLUMN "credit_transaction_id";