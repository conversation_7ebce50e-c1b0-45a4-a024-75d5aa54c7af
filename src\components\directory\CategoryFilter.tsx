import { type categories, items } from '@/db/schema';
import type { InferSelectModel } from 'drizzle-orm';

type Category = InferSelectModel<typeof categories>;
import { Button } from '@/components/ui/button';

interface Props {
  categories: Category[];
  onCategoryClick?: (categoryId: string) => void;
}

export default function CategoryFilter({ categories, onCategoryClick }: Props) {
  return (
    <div className="flex flex-wrap gap-2">
      {categories.map((category) => (
        <Button key={category.id} variant="outline" className="rounded-full">
          onClick={() => props.onCategoryClick?.(category.id)}>{category.name}
        </Button>
      ))}
    </div>
  );
}
