import { type categories, items } from '@/db/schema';
import type { InferSelectModel } from 'drizzle-orm';

type Category = InferSelectModel<typeof categories>;
import { Button } from '@/components/ui/button';

interface Props {
  categories: Category[];
  selectedCategory?: string | null;
  onCategoryClick?: (categoryId: string | null) => void;
}

export default function CategoryFilter({
  categories,
  selectedCategory,
  onCategoryClick,
}: Props) {
  return (
    <div className="flex flex-wrap gap-2">
      <Button
        variant={selectedCategory === null ? 'default' : 'outline'}
        className="rounded-full"
        onClick={() => onCategoryClick?.(null)}
      >
        全部
      </Button>
      {categories.map((category) => (
        <Button
          key={category.id}
          variant={selectedCategory === category.id ? 'default' : 'outline'}
          className="rounded-full"
          onClick={() => onCategoryClick?.(category.id)}
        >
          {category.name}
        </Button>
      ))}
    </div>
  );
}
