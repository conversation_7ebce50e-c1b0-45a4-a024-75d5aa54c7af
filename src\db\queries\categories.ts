import { desc, eq } from "drizzle-orm";
import { getDb } from "..";
import { categories, tags } from "../schema";
import { randomUUID } from "crypto";

export type CategoryInsert = typeof categories.$inferInsert;
export type TagInsert = typeof tags.$inferInsert;

/**
 * 获取所有分类
 */
export async function getAllCategories() {
  const db = await getDb();
  return await db
    .select()
    .from(categories)
    .orderBy(desc(categories.priority), categories.name);
}

/**
 * 根据ID获取分类
 */
export async function getCategoryById(id: string) {
  const db = await getDb();
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.id, id))
    .limit(1);

  return category || null;
}

/**
 * 根据slug获取分类
 */
export async function getCategoryBySlug(slug: string) {
  const db = await getDb();
  const [category] = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, slug))
    .limit(1);

  return category || null;
}

/**
 * 创建新分类
 */
export async function createCategory(data: CategoryInsert) {
  const db = await getDb();
  const [newCategory] = await db.insert(categories).values(data).returning();
  return newCategory;
}

/**
 * 获取所有标签
 */
export async function getAllTags() {
  const db = await getDb();
  return await db
    .select()
    .from(tags)
    .orderBy(tags.name);
}

/**
 * 搜索标签
 */
export async function searchTags(query: string, limit: number = 10) {
  const db = await getDb();
  return await db
    .select()
    .from(tags)
    .where(eq(tags.name, query))
    .limit(limit);
}

/**
 * 根据ID获取标签
 */
export async function getTagById(id: string) {
  const db = await getDb();
  const [tag] = await db
    .select()
    .from(tags)
    .where(eq(tags.id, id))
    .limit(1);

  return tag || null;
}

/**
 * 创建或获取标签
 */
export async function createOrGetTag(name: string, slug: string) {
  const db = await getDb();

  // 首先检查是否已存在
  const [existingTag] = await db
    .select()
    .from(tags)
    .where(eq(tags.name, name))
    .limit(1);

  if (existingTag) {
    return existingTag;
  }

  // 创建新标签
  const [newTag] = await db.insert(tags).values({
    id: randomUUID(),
    name,
    slug,
    createdAt: new Date(),
    updatedAt: new Date(),
  }).returning();

  return newTag;
}

/**
 * 创建新标签
 */
export async function createTag(data: TagInsert) {
  const db = await getDb();
  const [newTag] = await db.insert(tags).values(data).returning();
  return newTag;
}
