import { getCategoryBySlug } from '@/db/queries/categories';
import { getPublishedItems } from '@/db/queries/items';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import CategoryContent from './CategoryContent';

interface CategoryPageProps {
  params: Promise<{ locale: Locale; slug: string }>;
  searchParams: Promise<{ page?: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale; slug: string }>;
}): Promise<Metadata | undefined> {
  const { locale, slug } = await params;
  const category = await getCategoryBySlug(slug);
  
  if (!category) {
    return undefined;
  }

  const t = await getTranslations({ locale, namespace: 'CategoryPage' });

  return constructMetadata({
    title: `${category.name} - ${t('title')}`,
    description: category.description || `${t('description')} ${category.name}`,
    canonicalUrl: getUrlWithLocale(`/category/${slug}`, locale),
  });
}

export default async function CategoryPage(props: CategoryPageProps) {
  const params = await props.params;
  const searchParams = await props.searchParams;
  const { locale, slug } = params;
  const page = Number(searchParams.page) || 1;
  const itemsPerPage = 12;

  const category = await getCategoryBySlug(slug);
  
  if (!category) {
    notFound();
  }

  const items = await getPublishedItems({
    categoryId: category.id,
    page,
    limit: itemsPerPage,
  });

  const t = await getTranslations('CategoryPage');

  return (
    <CategoryContent
      locale={locale}
      category={category}
      items={items}
      currentPage={page}
      itemsPerPage={itemsPerPage}
      translations={{
        title: t('title'),
        description: t('description'),
        noItems: t('noItems'),
        loadMore: t('loadMore'),
        itemsFound: t('itemsFound'),
      }}
    />
  );
}
