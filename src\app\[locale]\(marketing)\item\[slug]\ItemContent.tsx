'use client';

import ItemGrid from '@/components/directory/ItemGrid';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import type { ItemWithRelations } from '@/db/queries/items';
import { Calendar, ChevronLeft, ExternalLink, Flag, Share2, User } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface Props {
  locale: string;
  item: ItemWithRelations;
  relatedItems: ItemWithRelations[];
  translations: {
    title: string;
    description: string;
    visitWebsite: string;
    relatedItems: string;
    categories: string;
    tags: string;
    submittedBy: string;
    submittedOn: string;
    share: string;
    report: string;
  };
}

export default function ItemContent({
  locale,
  item,
  relatedItems,
  translations,
}: Props) {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: item.name,
          text: item.description || '',
          url: window.location.href,
        });
      } catch (error) {
        console.log('分享失败:', error);
      }
    } else {
      // 复制链接到剪贴板
      navigator.clipboard.writeText(window.location.href);
      alert('链接已复制到剪贴板');
    }
  };

  return (
    <div className="container py-8">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
        <Link href="/" className="hover:text-foreground">
          首页
        </Link>
        <span>/</span>
        {item.categories.length > 0 && (
          <>
            <Link href={`/category/${item.categories[0].slug}`} className="hover:text-foreground">
              {item.categories[0].name}
            </Link>
            <span>/</span>
          </>
        )}
        <span className="text-foreground">{item.name}</span>
      </nav>

      {/* 返回按钮 */}
      <div className="mb-6">
        <Link href="/" className="flex items-center gap-2 text-muted-foreground hover:text-foreground">
          <ChevronLeft className="h-4 w-4" />
          返回首页
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 项目标题和操作 */}
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold mb-2">{item.name}</h1>
              {item.featured && (
                <Badge className="mb-4">精选项目</Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                {translations.share}
              </Button>
              <Button variant="outline" size="sm">
                <Flag className="h-4 w-4 mr-2" />
                {translations.report}
              </Button>
            </div>
          </div>

          {/* 项目图片 */}
          {item.imageUrl && (
            <div className="relative overflow-hidden rounded-lg">
              <Image
                src={item.imageUrl}
                alt={item.name}
                width={800}
                height={400}
                className="object-cover w-full h-64 md:h-96"
              />
            </div>
          )}

          {/* 项目描述 */}
          <div>
            <h2 className="text-xl font-semibold mb-4">项目介绍</h2>
            <p className="text-muted-foreground leading-relaxed">
              {item.description || '暂无详细介绍'}
            </p>
          </div>

          {/* 访问网站按钮 */}
          <div>
            <a
              href={item.link}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block"
            >
              <Button size="lg" className="w-full md:w-auto">
                <ExternalLink className="h-5 w-5 mr-2" />
                {translations.visitWebsite}
              </Button>
            </a>
          </div>
        </div>

        {/* 侧边栏 */}
        <div className="space-y-6">
          {/* 项目信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">项目信息</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* 提交者 */}
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {translations.submittedBy}
                </span>
                <span className="text-sm font-medium">
                  {item.submitter?.name || '匿名用户'}
                </span>
              </div>

              {/* 提交时间 */}
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {translations.submittedOn}
                </span>
                <span className="text-sm">
                  {new Date(item.createdAt).toLocaleDateString('zh-CN')}
                </span>
              </div>

              <Separator />

              {/* 分类 */}
              {item.categories.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">{translations.categories}</h4>
                  <div className="flex flex-wrap gap-2">
                    {item.categories.map((category) => (
                      <Link key={category.id} href={`/category/${category.slug}`}>
                        <Badge variant="secondary" className="hover:bg-secondary/80">
                          {category.name}
                        </Badge>
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* 标签 */}
              {item.tags.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">{translations.tags}</h4>
                  <div className="flex flex-wrap gap-2">
                    {item.tags.map((tag) => (
                      <Badge key={tag.id} variant="outline">
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 相关项目 */}
      {relatedItems.length > 0 && (
        <div className="mt-12">
          <h2 className="text-2xl font-bold mb-6">{translations.relatedItems}</h2>
          <ItemGrid items={relatedItems} />
        </div>
      )}
    </div>
  );
}
