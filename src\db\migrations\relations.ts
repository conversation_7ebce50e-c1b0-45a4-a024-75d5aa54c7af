import { relations } from "drizzle-orm/relations";
import { user, creditTransaction, items, payment, session, account, userCredit, itemTags, tags, itemCategories, categories } from "./schema";

export const creditTransactionRelations = relations(creditTransaction, ({one}) => ({
	user: one(user, {
		fields: [creditTransaction.userId],
		references: [user.id]
	}),
}));

export const userRelations = relations(user, ({many}) => ({
	creditTransactions: many(creditTransaction),
	items: many(items),
	payments: many(payment),
	sessions: many(session),
	accounts: many(account),
	userCredits: many(userCredit),
}));

export const itemsRelations = relations(items, ({one, many}) => ({
	user: one(user, {
		fields: [items.submitterId],
		references: [user.id]
	}),
	itemTags: many(itemTags),
	itemCategories: many(itemCategories),
}));

export const paymentRelations = relations(payment, ({one}) => ({
	user: one(user, {
		fields: [payment.userId],
		references: [user.id]
	}),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const userCreditRelations = relations(userCredit, ({one}) => ({
	user: one(user, {
		fields: [userCredit.userId],
		references: [user.id]
	}),
}));

export const itemTagsRelations = relations(itemTags, ({one}) => ({
	item: one(items, {
		fields: [itemTags.itemId],
		references: [items.id]
	}),
	tag: one(tags, {
		fields: [itemTags.tagId],
		references: [tags.id]
	}),
}));

export const tagsRelations = relations(tags, ({many}) => ({
	itemTags: many(itemTags),
}));

export const itemCategoriesRelations = relations(itemCategories, ({one}) => ({
	item: one(items, {
		fields: [itemCategories.itemId],
		references: [items.id]
	}),
	category: one(categories, {
		fields: [itemCategories.categoryId],
		references: [categories.id]
	}),
}));

export const categoriesRelations = relations(categories, ({many}) => ({
	itemCategories: many(itemCategories),
}));