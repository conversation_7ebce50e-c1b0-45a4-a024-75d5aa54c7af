import { randomUUID } from 'crypto';
import { auth } from '@/auth';
import { addItemCategories, createItem } from '@/db/queries/items';
import { type NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: '未认证用户' },
        { status: 401 }
      );
    }
    const userId = session.user.id;

    const newItem = await createItem({
      id: randomUUID(),
      name: data.name,
      description: data.description,
      link: data.link,
      imageUrl: data.imageUrl || null,
      submitterId: userId,
      freePlanStatus: 'pending',
      pricePlan: 'FREE',
      featured: false,
      slug: `${data.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now().toString(36).slice(-4)}`,
    });

    if (data.categoryIds?.length) {
      await addItemCategories(newItem.id, data.categoryIds);
    }

    return NextResponse.json({ success: true, itemId: newItem.id });
  } catch (error) {
    console.error('API提交失败:', error);
    return NextResponse.json(
      { success: false, error: '提交失败' },
      { status: 500 }
    );
  }
}
