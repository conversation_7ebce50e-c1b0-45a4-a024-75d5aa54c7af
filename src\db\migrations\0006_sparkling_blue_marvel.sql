DROP INDEX "category_slug_idx";--> statement-breakpoint
DROP INDEX "category_priority_idx";--> statement-breakpoint
DROP INDEX "item_categories_item_idx";--> statement-breakpoint
DROP INDEX "item_categories_category_idx";--> statement-breakpoint
DROP INDEX "item_tags_item_idx";--> statement-breakpoint
DROP INDEX "item_tags_tag_idx";--> statement-breakpoint
DROP INDEX "item_slug_idx";--> statement-breakpoint
DROP INDEX "item_submitter_idx";--> statement-breakpoint
DROP INDEX "item_featured_idx";--> statement-breakpoint
DROP INDEX "item_paid_idx";--> statement-breakpoint
DROP INDEX "item_publish_date_idx";--> statement-breakpoint
DROP INDEX "item_created_at_idx";--> statement-breakpoint
DROP INDEX "tag_slug_idx";--> statement-breakpoint
DROP INDEX "tag_name_idx";--> statement-breakpoint
ALTER TABLE "categories" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "categories" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "item_categories" ALTER COLUMN "item_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "item_categories" ALTER COLUMN "category_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "item_tags" ALTER COLUMN "item_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "item_tags" ALTER COLUMN "tag_id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "items" ALTER COLUMN "free_plan_status" SET DEFAULT 'submitting';--> statement-breakpoint
ALTER TABLE "tags" ALTER COLUMN "id" SET DATA TYPE uuid;--> statement-breakpoint
ALTER TABLE "tags" ALTER COLUMN "id" SET DEFAULT gen_random_uuid();--> statement-breakpoint
ALTER TABLE "items" ADD COLUMN "credit_transaction_id" text;--> statement-breakpoint
ALTER TABLE "items" ADD CONSTRAINT "items_credit_transaction_id_credit_transaction_id_fk" FOREIGN KEY ("credit_transaction_id") REFERENCES "public"."credit_transaction"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "categories_slug_idx" ON "categories" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "categories_priority_idx" ON "categories" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "item_categories_item_id_idx" ON "item_categories" USING btree ("item_id");--> statement-breakpoint
CREATE INDEX "item_categories_category_id_idx" ON "item_categories" USING btree ("category_id");--> statement-breakpoint
CREATE INDEX "item_tags_item_id_idx" ON "item_tags" USING btree ("item_id");--> statement-breakpoint
CREATE INDEX "item_tags_tag_id_idx" ON "item_tags" USING btree ("tag_id");--> statement-breakpoint
CREATE INDEX "items_slug_idx" ON "items" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "items_submitter_id_idx" ON "items" USING btree ("submitter_id");--> statement-breakpoint
CREATE INDEX "items_featured_idx" ON "items" USING btree ("featured");--> statement-breakpoint
CREATE INDEX "items_paid_idx" ON "items" USING btree ("paid");--> statement-breakpoint
CREATE INDEX "items_created_at_idx" ON "items" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "tags_slug_idx" ON "tags" USING btree ("slug");--> statement-breakpoint
CREATE INDEX "tags_name_idx" ON "tags" USING btree ("name");--> statement-breakpoint
ALTER TABLE "categories" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "item_categories" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "item_tags" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "tags" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "tags" ADD CONSTRAINT "tags_name_unique" UNIQUE("name");