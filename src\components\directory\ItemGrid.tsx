import { items } from "@/db/schema";
import type { InferSelectModel } from "drizzle-orm";

type Item = InferSelectModel<typeof items>;
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import Image from "next/image";

interface Props {
  items: Item[];
}

export default function ItemGrid({ items }: Props) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {items.map((item) => (
        <Card key={item.id} className="hover:shadow-lg transition-shadow">
          <CardHeader>
            {item.imageUrl && (
              <Image
                src={item.imageUrl}
                alt={item.name}
                width={300}
                height={200}
                className="rounded-t-lg object-cover"
              />
            )}
          </CardHeader>
          <CardContent className="p-4">
            <h3 className="font-semibold text-lg">{item.name}</h3>
            <p className="text-muted-foreground mt-2">{item.description}</p>
            <a
              href={item.link}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary mt-4 inline-block"
            >
              访问网站 →
            </a>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
