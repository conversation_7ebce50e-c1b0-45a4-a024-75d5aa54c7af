import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import type { ItemWithRelations } from '@/db/queries/items';
import { ExternalLink } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface Props {
  items: ItemWithRelations[];
  viewMode?: 'grid' | 'list';
}

export default function ItemGrid({ items, viewMode = 'grid' }: Props) {
  if (viewMode === 'list') {
    return (
      <div className="space-y-4">
        {items.map((item) => (
          <Card key={item.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex gap-4">
                {item.imageUrl && (
                  <div className="flex-shrink-0">
                    <Image
                      src={item.imageUrl}
                      alt={item.name}
                      width={120}
                      height={80}
                      className="rounded-lg object-cover"
                    />
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <Link href={`/item/${item.slug}`}>
                        <h3 className="font-semibold text-lg hover:text-primary transition-colors">
                          {item.name}
                        </h3>
                      </Link>
                      <p className="text-muted-foreground mt-1 line-clamp-2">
                        {item.description}
                      </p>
                    </div>
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1 text-primary hover:text-primary/80 transition-colors"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </a>
                  </div>

                  {/* 分类和标签 */}
                  <div className="flex flex-wrap gap-2 mt-3">
                    {item.categories.map((category) => (
                      <Badge key={category.id} variant="secondary">
                        {category.name}
                      </Badge>
                    ))}
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag.id} variant="outline">
                        {tag.name}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {items.map((item) => (
        <Card key={item.id} className="hover:shadow-lg transition-shadow group">
          <CardHeader className="p-0">
            {item.imageUrl && (
              <div className="relative overflow-hidden rounded-t-lg">
                <Image
                  src={item.imageUrl}
                  alt={item.name}
                  width={400}
                  height={240}
                  className="object-cover w-full h-48 group-hover:scale-105 transition-transform duration-300"
                />
                {item.featured && (
                  <Badge className="absolute top-2 right-2">精选</Badge>
                )}
              </div>
            )}
          </CardHeader>
          <CardContent className="p-4">
            <div className="flex items-start justify-between mb-2">
              <Link href={`/item/${item.slug}`}>
                <h3 className="font-semibold text-lg hover:text-primary transition-colors line-clamp-1">
                  {item.name}
                </h3>
              </Link>
              <a
                href={item.link}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-1 text-primary hover:text-primary/80 transition-colors"
              >
                <ExternalLink className="h-4 w-4" />
              </a>
            </div>

            <p className="text-muted-foreground text-sm line-clamp-2 mb-3">
              {item.description}
            </p>

            {/* 分类和标签 */}
            <div className="flex flex-wrap gap-1">
              {item.categories.slice(0, 2).map((category) => (
                <Badge
                  key={category.id}
                  variant="secondary"
                  className="text-xs"
                >
                  {category.name}
                </Badge>
              ))}
              {item.tags.slice(0, 2).map((tag) => (
                <Badge key={tag.id} variant="outline" className="text-xs">
                  {tag.name}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
