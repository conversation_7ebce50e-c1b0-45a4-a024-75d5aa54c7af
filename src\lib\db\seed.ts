import { getDb } from '@/db'
import { categories, tags } from '@/db/schema'
import { randomBytes } from 'crypto'

// 生成简单的ID
function generateId() {
  return randomBytes(10).toString('hex')
}

// 初始分类数据
const initialCategories = [
  {
    id: generateId(),
    name: 'AI工具',
    slug: 'ai-tools',
    description: 'AI驱动的工具和服务',
    priority: 100,
  },
  {
    id: generateId(),
    name: '开发工具',
    slug: 'dev-tools',
    description: '开发者必备工具',
    priority: 90,
  },
  {
    id: generateId(),
    name: '设计资源',
    slug: 'design-tools',
    description: '设计师专用工具和资源',
    priority: 80,
  },
  {
    id: generateId(),
    name: '生产力工具',
    slug: 'productivity',
    description: '提升工作效率的工具',
    priority: 70,
  },
  {
    id: generateId(),
    name: '营销工具',
    slug: 'marketing',
    description: '营销推广相关工具',
    priority: 60,
  },
  {
    id: generateId(),
    name: '数据分析',
    slug: 'analytics',
    description: '数据分析和可视化工具',
    priority: 50,
  },
  {
    id: generateId(),
    name: '内容创作',
    slug: 'content-creation',
    description: '内容创作和编辑工具',
    priority: 40,
  },
  {
    id: generateId(),
    name: '社交媒体',
    slug: 'social-media',
    description: '社交媒体管理工具',
    priority: 30,
  },
  {
    id: generateId(),
    name: '电商工具',
    slug: 'ecommerce',
    description: '电子商务相关工具',
    priority: 20,
  },
  {
    id: generateId(),
    name: '其他工具',
    slug: 'others',
    description: '其他实用工具',
    priority: 10,
  },
]

// 初始标签数据
const initialTags = [
  // AI相关
  { id: generateId(), name: 'ChatGPT', slug: 'chatgpt', color: '#10b981' },
  { id: generateId(), name: '图像生成', slug: 'image-generation', color: '#8b5cf6' },
  { id: generateId(), name: '文本处理', slug: 'text-processing', color: '#06b6d4' },
  { id: generateId(), name: '语音合成', slug: 'text-to-speech', color: '#f59e0b' },
  { id: generateId(), name: '机器学习', slug: 'machine-learning', color: '#ef4444' },
  
  // 开发相关
  { id: generateId(), name: 'API', slug: 'api', color: '#3b82f6' },
  { id: generateId(), name: '数据库', slug: 'database', color: '#6366f1' },
  { id: generateId(), name: '部署', slug: 'deployment', color: '#8b5cf6' },
  { id: generateId(), name: '监控', slug: 'monitoring', color: '#10b981' },
  { id: generateId(), name: '测试', slug: 'testing', color: '#06b6d4' },
  
  // 设计相关
  { id: generateId(), name: 'UI设计', slug: 'ui-design', color: '#ec4899' },
  { id: generateId(), name: '图标', slug: 'icons', color: '#f59e0b' },
  { id: generateId(), name: '配色', slug: 'color-palette', color: '#ef4444' },
  { id: generateId(), name: '字体', slug: 'fonts', color: '#8b5cf6' },
  { id: generateId(), name: '原型', slug: 'prototyping', color: '#10b981' },
  
  // 生产力相关
  { id: generateId(), name: '笔记', slug: 'notes', color: '#06b6d4' },
  { id: generateId(), name: '时间管理', slug: 'time-management', color: '#f59e0b' },
  { id: generateId(), name: '团队协作', slug: 'collaboration', color: '#10b981' },
  { id: generateId(), name: '文档', slug: 'documentation', color: '#6366f1' },
  { id: generateId(), name: '自动化', slug: 'automation', color: '#8b5cf6' },
  
  // 其他
  { id: generateId(), name: '免费', slug: 'free', color: '#10b981' },
  { id: generateId(), name: '开源', slug: 'open-source', color: '#3b82f6' },
  { id: generateId(), name: '跨平台', slug: 'cross-platform', color: '#6366f1' },
  { id: generateId(), name: '移动端', slug: 'mobile', color: '#ec4899' },
  { id: generateId(), name: 'Chrome扩展', slug: 'chrome-extension', color: '#f59e0b' },
]

// 种子数据函数
export async function seedCategoriesAndTags() {
  const db = await getDb()
  
  try {
    console.log('开始插入分类数据...')
    await db.insert(categories).values(initialCategories).onConflictDoNothing()
    console.log(`✅ 已插入 ${initialCategories.length} 个分类`)
    
    console.log('开始插入标签数据...')
    await db.insert(tags).values(initialTags).onConflictDoNothing()
    console.log(`✅ 已插入 ${initialTags.length} 个标签`)
    
    console.log('🎉 种子数据插入完成！')
  } catch (error) {
    console.error('❌ 种子数据插入失败:', error)
    throw error
  }
}

// 如果直接运行此文件，则执行种子数据插入
if (require.main === module) {
  seedCategoriesAndTags()
    .then(() => {
      console.log('种子数据插入完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('种子数据插入失败:', error)
      process.exit(1)
    })
}
