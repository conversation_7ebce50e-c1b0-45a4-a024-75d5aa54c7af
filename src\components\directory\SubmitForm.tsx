'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import type { categories } from '@/db/schema';
import { authClient } from '@/lib/auth-client';
import type { InferSelectModel } from 'drizzle-orm';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

type Category = InferSelectModel<typeof categories>;

interface Props {
  categories: Category[];
}

interface FormData {
  name: string;
  description: string;
  link: string;
  categoryIds: string[];
  imageUrl?: string;
}

export default function SubmitForm({ categories }: Props) {
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<FormData>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    try {
      const { data: session } = await authClient.getSession();
      if (!session?.user?.id) {
        alert('请先登录后再提交');
        return;
      }
      const userId = session.user.id;

      const response = await fetch('/api/directory/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${session.sessionToken}`,
        },
        body: JSON.stringify({
          ...data,
        }),
      });

      if (!response.ok) {
        throw new Error('提交失败');
      }

      setSubmitSuccess(true);
    } catch (error) {
      console.error('提交失败:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <div className="text-center py-8">
        <h2 className="text-2xl font-bold mb-4">提交成功！</h2>
        <p>感谢您的提交，我们会尽快审核。</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4 max-w-2xl">
      <div>
        <label htmlFor="name">项目名称</label>
        <Input
          id="name"
          {...register('name', { required: '请输入项目名称' })}
        />
        {errors.name && <p className="text-red-500">{errors.name.message}</p>}
      </div>

      <div>
        <label htmlFor="description">项目描述</label>
        <Textarea
          id="description"
          {...register('description', { required: '请输入项目描述' })}
        />
        {errors.description && (
          <p className="text-red-500">{errors.description.message}</p>
        )}
      </div>

      <div>
        <label htmlFor="link">项目链接</label>
        <Input
          id="link"
          type="url"
          {...register('link', { required: '请输入项目链接' })}
        />
        {errors.link && <p className="text-red-500">{errors.link.message}</p>}
      </div>

      <div>
        <label htmlFor="categories-select">分类</label>
        <Select>
          <SelectTrigger id="categories-select">
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? '提交中...' : '提交项目'}
      </Button>
    </form>
  );
}
