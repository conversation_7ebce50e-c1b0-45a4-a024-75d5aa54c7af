import type { ComponentType } from 'react';
import type { InferSelectModel } from 'drizzle-orm';
import type { items, categories } from '@/db/schema';

type Item = InferSelectModel<typeof items>;
type Category = InferSelectModel<typeof categories>;

declare module '@/app/[locale]/(marketing)/(home)/HomeContent' {
  interface Props {
    initialItems: Item[];
    categories: Category[];
    t: (key: string) => string;
    locale: string;
  }

  const HomeContent: ComponentType<Props>;
  export default HomeContent;
}
