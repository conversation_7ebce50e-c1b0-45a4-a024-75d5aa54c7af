import { createItem } from "@/db/queries/items";
import { getAllCategories } from "@/db/queries/categories";
import SubmitForm from "@/components/directory/SubmitForm";
import { getTranslations } from "next-intl/server";

export default async function SubmitPage() {
  const categories = await getAllCategories();
  const t = await getTranslations('SubmitPage');

  return (
    <div className="container py-8">
      <h1 className="text-3xl font-bold mb-6">{t('title')}</h1>
      <SubmitForm categories={categories} />
    </div>
  );
}
