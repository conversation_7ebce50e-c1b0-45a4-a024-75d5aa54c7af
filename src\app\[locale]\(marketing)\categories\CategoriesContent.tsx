'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type { categories } from '@/db/schema';
import type { InferSelectModel } from 'drizzle-orm';
import { Folder } from 'lucide-react';
import Link from 'next/link';

type Category = InferSelectModel<typeof categories>;

interface Props {
  locale: string;
  categories: Category[];
  translations: {
    title: string;
    description: string;
    browseCategory: string;
    itemsCount: string;
  };
}

export default function CategoriesContent({
  locale,
  categories,
  translations,
}: Props) {
  return (
    <div className="container py-8">
      {/* 页面标题 */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{translations.title}</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          {translations.description}
        </p>
      </div>

      {/* 分类网格 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {categories.map((category) => (
          <Link key={category.id} href={`/category/${category.slug}`}>
            <Card className="hover:shadow-lg transition-all duration-300 hover:scale-105 group">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto mb-4 p-3 bg-primary/10 rounded-full w-fit group-hover:bg-primary/20 transition-colors">
                  <Folder className="h-8 w-8 text-primary" />
                </div>
                <CardTitle className="text-lg group-hover:text-primary transition-colors">
                  {category.name}
                </CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                {category.description && (
                  <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                    {category.description}
                  </p>
                )}
                <Badge variant="secondary" className="text-xs">
                  {translations.itemsCount.replace('{count}', '0')}
                </Badge>
              </CardContent>
            </Card>
          </Link>
        ))}
      </div>

      {/* 空状态 */}
      {categories.length === 0 && (
        <div className="text-center py-12">
          <Folder className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">暂无分类</h3>
          <p className="text-muted-foreground">
            分类正在建设中，敬请期待...
          </p>
        </div>
      )}
    </div>
  );
}
