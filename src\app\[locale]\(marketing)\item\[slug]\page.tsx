import { getItemBySlug, getRelatedItems } from '@/db/queries/items';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import ItemContent from './ItemContent';

interface ItemPageProps {
  params: Promise<{ locale: Locale; slug: string }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale; slug: string }>;
}): Promise<Metadata | undefined> {
  const { locale, slug } = await params;
  const item = await getItemBySlug(slug);
  
  if (!item) {
    return undefined;
  }

  const t = await getTranslations({ locale, namespace: 'ItemPage' });

  return constructMetadata({
    title: `${item.name} - ${t('title')}`,
    description: item.description || `${t('description')} ${item.name}`,
    canonicalUrl: getUrlWithLocale(`/item/${slug}`, locale),
    image: item.imageUrl || undefined,
  });
}

export default async function ItemPage(props: ItemPageProps) {
  const params = await props.params;
  const { locale, slug } = params;

  const item = await getItemBySlug(slug);
  
  if (!item) {
    notFound();
  }

  // 获取相关项目
  const relatedItems = await getRelatedItems(item.id, {
    categoryIds: item.categories.map(c => c.id),
    limit: 6,
  });

  const t = await getTranslations('ItemPage');

  return (
    <ItemContent
      locale={locale}
      item={item}
      relatedItems={relatedItems}
      translations={{
        title: t('title'),
        description: t('description'),
        visitWebsite: t('visitWebsite'),
        relatedItems: t('relatedItems'),
        categories: t('categories'),
        tags: t('tags'),
        submittedBy: t('submittedBy'),
        submittedOn: t('submittedOn'),
        share: t('share'),
        report: t('report'),
      }}
    />
  );
}
