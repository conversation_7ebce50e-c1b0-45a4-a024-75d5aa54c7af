import { getAllCategories } from '@/db/queries/categories';
import { constructMetadata } from '@/lib/metadata';
import { getUrlWithLocale } from '@/lib/urls/urls';
import type { Metadata } from 'next';
import type { Locale } from 'next-intl';
import { getTranslations } from 'next-intl/server';
import CategoriesContent from './CategoriesContent';

interface CategoriesPageProps {
  params: Promise<{ locale: Locale }>;
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: Locale }>;
}): Promise<Metadata | undefined> {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'CategoriesPage' });

  return constructMetadata({
    title: t('title'),
    description: t('description'),
    canonicalUrl: getUrlWithLocale('/categories', locale),
  });
}

export default async function CategoriesPage(props: CategoriesPageProps) {
  const params = await props.params;
  const { locale } = params;

  const categories = await getAllCategories();
  const t = await getTranslations('CategoriesPage');

  return (
    <CategoriesContent
      locale={locale}
      categories={categories}
      translations={{
        title: t('title'),
        description: t('description'),
        browseCategory: t('browseCategory'),
        itemsCount: t('itemsCount'),
      }}
    />
  );
}
