import { getDb } from '@/db'
import { categories, tags, itemCategories, itemTags, items } from '@/db/schema'
import { eq, desc, asc, sql } from 'drizzle-orm'

export type Category = typeof categories.$inferSelect
export type NewCategory = typeof categories.$inferInsert
export type Tag = typeof tags.$inferSelect
export type NewTag = typeof tags.$inferInsert

// =====================================
// 分类相关查询
// =====================================

// 获取所有分类（带项目数量统计）
export async function getCategories() {
  const db = await getDb()
  
  const result = await db
    .select({
      category: categories,
      itemCount: sql<number>`count(${itemCategories.itemId})`.as('itemCount'),
    })
    .from(categories)
    .leftJoin(itemCategories, eq(categories.id, itemCategories.categoryId))
    .leftJoin(items, eq(itemCategories.itemId, items.id))
    .where(eq(items.freePlanStatus, 'published'))
    .groupBy(categories.id)
    .orderBy(desc(categories.priority), asc(categories.name))

  return result
}

// 根据slug获取分类详情
export async function getCategoryBySlug(slug: string) {
  const db = await getDb()
  
  const result = await db
    .select()
    .from(categories)
    .where(eq(categories.slug, slug))

  return result[0] || null
}

// 创建新分类
export async function createCategory(data: NewCategory) {
  const db = await getDb()
  const result = await db.insert(categories).values(data).returning()
  return result[0]
}

// 获取分类下的项目ID
export async function getCategoryItemIds(categoryId: string) {
  const db = await getDb()
  
  const result = await db
    .select({
      itemId: itemCategories.itemId
    })
    .from(itemCategories)
    .where(eq(itemCategories.categoryId, categoryId))

  return result.map(r => r.itemId)
}

// =====================================
// 标签相关查询
// =====================================

// 获取所有标签（带项目数量统计）
export async function getTags() {
  const db = await getDb()
  
  const result = await db
    .select({
      tag: tags,
      itemCount: sql<number>`count(${itemTags.itemId})`.as('itemCount'),
    })
    .from(tags)
    .leftJoin(itemTags, eq(tags.id, itemTags.tagId))
    .leftJoin(items, eq(itemTags.itemId, items.id))
    .where(eq(items.freePlanStatus, 'published'))
    .groupBy(tags.id)
    .orderBy(desc(sql`count(${itemTags.itemId})`), asc(tags.name))

  return result
}

// 获取热门标签
export async function getPopularTags(limit = 20) {
  const db = await getDb()
  
  const result = await db
    .select({
      tag: tags,
      itemCount: sql<number>`count(${itemTags.itemId})`.as('itemCount'),
    })
    .from(tags)
    .leftJoin(itemTags, eq(tags.id, itemTags.tagId))
    .leftJoin(items, eq(itemTags.itemId, items.id))
    .where(eq(items.freePlanStatus, 'published'))
    .groupBy(tags.id)
    .having(sql`count(${itemTags.itemId}) > 0`)
    .orderBy(desc(sql`count(${itemTags.itemId})`))
    .limit(limit)

  return result
}

// 根据slug获取标签详情
export async function getTagBySlug(slug: string) {
  const db = await getDb()
  
  const result = await db
    .select()
    .from(tags)
    .where(eq(tags.slug, slug))

  return result[0] || null
}

// 创建新标签
export async function createTag(data: NewTag) {
  const db = await getDb()
  const result = await db.insert(tags).values(data).returning()
  return result[0]
}

// 获取标签下的项目ID
export async function getTagItemIds(tagId: string) {
  const db = await getDb()
  
  const result = await db
    .select({
      itemId: itemTags.itemId
    })
    .from(itemTags)
    .where(eq(itemTags.tagId, tagId))

  return result.map(r => r.itemId)
}

// =====================================
// 关系表操作
// =====================================

// 为项目添加分类
export async function addItemCategory(itemId: string, categoryId: string) {
  const db = await getDb()
  
  await db.insert(itemCategories).values({
    itemId,
    categoryId,
  }).onConflictDoNothing()
}

// 为项目添加标签
export async function addItemTag(itemId: string, tagId: string) {
  const db = await getDb()
  
  await db.insert(itemTags).values({
    itemId,
    tagId,
  }).onConflictDoNothing()
}

// 移除项目的所有分类
export async function removeItemCategories(itemId: string) {
  const db = await getDb()
  
  await db.delete(itemCategories)
    .where(eq(itemCategories.itemId, itemId))
}

// 移除项目的所有标签
export async function removeItemTags(itemId: string) {
  const db = await getDb()
  
  await db.delete(itemTags)
    .where(eq(itemTags.itemId, itemId))
}

// 获取项目的分类
export async function getItemCategories(itemId: string) {
  const db = await getDb()
  
  const result = await db
    .select({
      category: categories
    })
    .from(itemCategories)
    .innerJoin(categories, eq(itemCategories.categoryId, categories.id))
    .where(eq(itemCategories.itemId, itemId))

  return result.map(r => r.category)
}

// 获取项目的标签
export async function getItemTags(itemId: string) {
  const db = await getDb()
  
  const result = await db
    .select({
      tag: tags
    })
    .from(itemTags)
    .innerJoin(tags, eq(itemTags.tagId, tags.id))
    .where(eq(itemTags.itemId, itemId))

  return result.map(r => r.tag)
}
