---
title: 什么是 Fumadocs
description: 介绍 Fumadocs，一个可以打破常规的文档框架
image: /images/blog/post-1.png
date: "2025-04-01"
published: true
categories: [company, product]
author: fox
---

Fumadocs 的创建是因为我想要一种更加可定制化的文档构建体验，一个不固执己见的文档框架，**一个你可以"打破"的"框架"**。

## 理念

**更少的抽象：** Fumadocs 期望您编写代码并与您的其余软件协作。
虽然大多数框架都是通过配置文件进行配置，但当您希望调整其细节时，它们通常缺乏灵活性。
您无法控制它们如何渲染页面或内部逻辑。Fumadocs 向您展示应用程序如何工作，而不是仅提供单一的配置文件。

**Next.js 基础：** 它为您提供实用工具和美观的 UI。
您仍然使用 Next.js App Router 的功能，如**静态站点生成**。对于 Next.js 开发者来说没有新的东西，所以您可以放心使用。

**对 UI 有自己的看法：** Fumadocs UI（默认主题）提供的唯一东西是**用户界面**。UI 的设计理念是提供更好的移动响应性和用户体验。
相反，我们使用受 Shadcn UI 启发的更灵活的方法 — [Fumadocs CLI](/docs/cli)，这样我们可以快速迭代设计，并欢迎更多关于 UI 的反馈。

## 为什么选择 Fumadocs

Fumadocs 的设计考虑了灵活性。

您可以将 `fumadocs-core` 用作无头 UI 库并带来您自己的样式。
Fumadocs MDX 也是处理 Next.js 中 MDX 内容的有用库。它还包括：

- 许多内置组件。
- Typescript Twoslash、OpenAPI 和 Math (KaTeX) 集成。
- 默认情况下快速且优化，原生构建在 App Router 上。
- 与 Next.js 紧密集成，您可以轻松将其添加到现有的 Next.js 项目中。

如果您感兴趣，可以阅读 [比较](/docs/comparisons)。

### 文档

Fumadocs 专注于**创作体验**，它提供了一个漂亮的主题和许多文档自动化工具。

它帮助您更快地迭代代码库，同时不会落下您的文档。
您可以将此站点作为使用 Fumadocs 构建的文档站点的示例。

### 博客站点

由于 Next.js 已经是一个强大的框架，大多数功能可以**仅使用 Next.js** 实现。

Fumadocs 为 Next.js 提供了额外的工具，包括语法高亮、文档搜索和默认主题（Fumadocs UI）。
它帮助您避免重新发明轮子。

## 何时使用 Fumadocs

对于大多数 Web 应用程序，原生 React.js 已经不够用了。
如今，我们还希望有一个博客、展示页面、FAQ 页面等。带有令人惊叹的精美 UI，在这些情况下，Fumadocs 可以帮助您更轻松地构建文档，减少样板代码。

Fumadocs 由 Fuma 和许多贡献者维护，关注代码库的可维护性。
虽然我们不打算提供人们想要的每一项功能，但我们更专注于使基本功能完美且维护良好。
您也可以通过贡献来帮助 Fumadocs 变得更加有用！