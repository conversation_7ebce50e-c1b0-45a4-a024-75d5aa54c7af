import { getDb } from './index';
import { categories, items, itemCategories, tags, itemTags } from './schema';
import { nanoid } from 'nanoid';

export async function seedTestData() {
  const db = await getDb();

  try {
    // 创建测试分类
    const testCategories = [
      { id: nanoid(), name: '开发', slug: 'development', description: '开发工具和资源' },
      { id: nanoid(), name: '设计', slug: 'design', description: '设计工具和资源' },
      { id: nanoid(), name: '效率工具', slug: 'productivity', description: '提高效率的工具' },
    ];

    await db.insert(categories).values(testCategories).onConflictDoNothing();

    // 创建测试标签
    const testTags = [
      { id: nanoid(), name: 'AI', slug: 'ai' },
      { id: nanoid(), name: '免费', slug: 'free' },
      { id: nanoid(), name: '开源', slug: 'open-source' },
    ];

    await db.insert(tags).values(testTags).onConflictDoNothing();

    // 创建测试项目
    const testItems = [
      {
        id: nanoid(),
        name: 'Notion',
        slug: 'notion',
        description: '一体化效率工具',
        link: 'https://www.notion.so',
        imageUrl: 'https://www.notion.so/images/meta/default.png',
        featured: true,
        freePlanStatus: 'approved',
        submitterId: 'test-user-id',
      },
      {
        id: nanoid(),
        name: 'Figma',
        slug: 'figma',
        description: '协作设计工具',
        link: 'https://www.figma.com',
        imageUrl: 'https://www.figma.com/images/meta/figma-meta.png',
        featured: true,
        freePlanStatus: 'approved',
        submitterId: 'test-user-id',
      },
      {
        id: nanoid(),
        name: 'VS Code',
        slug: 'vscode',
        description: '免费的代码编辑器',
        link: 'https://code.visualstudio.com',
        imageUrl: 'https://code.visualstudio.com/assets/images/code-stable.png',
        featured: false,
        freePlanStatus: 'approved',
        submitterId: 'test-user-id',
      },
    ];

    const insertedItems = await db.insert(items).values(testItems).returning();

    // 创建项目-分类关联
    const itemCategoryRelations = [
      { itemId: insertedItems[0].id, categoryId: testCategories[2].id }, // Notion -> 效率工具
      { itemId: insertedItems[1].id, categoryId: testCategories[1].id }, // Figma -> 设计
      { itemId: insertedItems[2].id, categoryId: testCategories[0].id }, // VS Code -> 开发
    ];

    await db.insert(itemCategories).values(itemCategoryRelations).onConflictDoNothing();

    // 创建项目-标签关联
    const itemTagRelations = [
      { itemId: insertedItems[0].id, tagId: testTags[1].id }, // Notion -> 免费
      { itemId: insertedItems[1].id, tagId: testTags[1].id }, // Figma -> 免费
      { itemId: insertedItems[2].id, tagId: testTags[1].id }, // VS Code -> 免费
      { itemId: insertedItems[2].id, tagId: testTags[2].id }, // VS Code -> 开源
    ];

    await db.insert(itemTags).values(itemTagRelations).onConflictDoNothing();

    console.log('测试数据创建成功！');
    return { categories: testCategories, items: insertedItems, tags: testTags };
  } catch (error) {
    console.error('创建测试数据失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，则执行种子数据
if (require.main === module) {
  seedTestData()
    .then(() => {
      console.log('种子数据创建完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('种子数据创建失败:', error);
      process.exit(1);
    });
}
