'use client';

import ItemGrid from '@/components/directory/ItemGrid';
import { Button } from '@/components/ui/button';
import type { ItemWithRelations } from '@/db/queries/items';
import type { categories } from '@/db/schema';
import type { InferSelectModel } from 'drizzle-orm';
import { ChevronLeft, Grid, List } from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

type Category = InferSelectModel<typeof categories>;

interface Props {
  locale: string;
  category: Category;
  items: ItemWithRelations[];
  currentPage: number;
  itemsPerPage: number;
  translations: {
    title: string;
    description: string;
    noItems: string;
    loadMore: string;
    itemsFound: string;
  };
}

export default function CategoryContent({
  locale,
  category,
  items,
  currentPage,
  itemsPerPage,
  translations,
}: Props) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const totalItems = items.length;
  const hasMoreItems = totalItems === itemsPerPage;

  return (
    <div className="container py-8">
      {/* 面包屑导航 */}
      <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
        <Link href="/" className="hover:text-foreground">
          首页
        </Link>
        <span>/</span>
        <Link href="/categories" className="hover:text-foreground">
          分类
        </Link>
        <span>/</span>
        <span className="text-foreground">{category.name}</span>
      </nav>

      {/* 分类标题和描述 */}
      <div className="mb-8">
        <div className="flex items-center gap-4 mb-4">
          <Link href="/" className="flex items-center gap-2 text-muted-foreground hover:text-foreground">
            <ChevronLeft className="h-4 w-4" />
            返回首页
          </Link>
        </div>
        
        <h1 className="text-3xl font-bold mb-4">{category.name}</h1>
        
        {category.description && (
          <p className="text-lg text-muted-foreground mb-6">
            {category.description}
          </p>
        )}

        {/* 统计信息和视图切换 */}
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            {translations.itemsFound.replace('{count}', totalItems.toString())}
          </p>
          
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 项目列表 */}
      {items.length > 0 ? (
        <div className="space-y-8">
          <ItemGrid items={items} viewMode={viewMode} />
          
          {/* 分页 */}
          {hasMoreItems && (
            <div className="flex justify-center">
              <Link href={`/category/${category.slug}?page=${currentPage + 1}`}>
                <Button variant="outline">
                  {translations.loadMore}
                </Button>
              </Link>
            </div>
          )}
        </div>
      ) : (
        <div className="text-center py-12">
          <p className="text-muted-foreground text-lg">
            {translations.noItems}
          </p>
          <Link href="/submit" className="mt-4 inline-block">
            <Button>
              提交第一个项目
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
