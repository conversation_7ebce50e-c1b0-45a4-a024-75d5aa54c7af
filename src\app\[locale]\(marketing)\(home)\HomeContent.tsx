'use client';

import HeroSection from '@/components/blocks/hero/hero';
import CategoryFilter from '@/components/directory/CategoryFilter';
import ItemGrid from '@/components/directory/ItemGrid';
import { getPublishedItems } from '@/db/queries/items';
import type { ItemWithRelations } from '@/db/queries/items';
import type { categories } from '@/db/schema';
import type { InferSelectModel } from 'drizzle-orm';
import { useState } from 'react';

type Category = InferSelectModel<typeof categories>;

interface Props {
  locale: string;
  initialItems: ItemWithRelations[];
  categories: Category[];
  translations: {
    featuredCategories: string;
    featuredTools: string;
    itemsInCategory: string;
  };
}

export default function HomeContent({
  locale,
  initialItems,
  categories,
  translations,
}: Props) {
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [items, setItems] = useState<ItemWithRelations[]>(initialItems);

  const handleCategoryClick = async (categoryId: string) => {
    setSelectedCategory(categoryId);
    const filteredItems = await getPublishedItems({
      categoryId,
      limit: 12,
    });
    setItems(filteredItems);
  };

  return (
    <div className="space-y-12">
      <HeroSection />
      <section className="container py-8">
        <h2 className="text-2xl font-bold mb-6">
          {translations.featuredCategories}
        </h2>
        <div className="mb-12">
          <CategoryFilter
            categories={categories}
            onCategoryClick={handleCategoryClick}
          />
        </div>
      </section>
      <section className="container py-8">
        <h2 className="text-2xl font-bold mb-6">
          {selectedCategory
            ? `${translations.itemsInCategory}: ${
                categories.find((c) => c.id === selectedCategory)?.name
              }`
            : translations.featuredTools}
        </h2>
        <ItemGrid items={items} />
      </section>
    </div>
  );
}
