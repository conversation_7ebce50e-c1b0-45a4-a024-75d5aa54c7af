import { getDb } from '@/db'
import { items, categories, tags, itemCategories, itemTags, user } from '@/db/schema'
import { eq, desc, asc, and, or, like, inArray, sql } from 'drizzle-orm'

export type Item = typeof items.$inferSelect
export type NewItem = typeof items.$inferInsert

// 获取所有项目（支持分页和筛选）
export async function getItems(options: {
  page?: number
  limit?: number
  categorySlug?: string
  tagSlug?: string
  featured?: boolean
  search?: string
  sortBy?: 'newest' | 'oldest' | 'featured'
} = {}) {
  const {
    page = 1,
    limit = 20,
    categorySlug,
    tagSlug,
    featured,
    search,
    sortBy = 'newest'
  } = options

  const offset = (page - 1) * limit
  const db = await getDb()

  // 构建基础查询
  const baseQuery = db
    .select({
      item: items,
      submitter: {
        id: user.id,
        name: user.name,
        image: user.image,
      },
    })
    .from(items)
    .leftJoin(user, eq(items.submitterId, user.id))
    .where(
      and(
        // 只显示已发布的项目
        eq(items.freePlanStatus, 'published'),
        // 如果只显示精选
        featured ? eq(items.featured, true) : undefined,
        // 如果有搜索关键词
        search ? or(
          like(items.name, `%${search}%`),
          like(items.description, `%${search}%`)
        ) : undefined
      )
    )

  // 应用排序
  let orderedQuery;
  switch (sortBy) {
    case 'newest':
      orderedQuery = baseQuery.orderBy(desc(items.createdAt))
      break
    case 'oldest':
      orderedQuery = baseQuery.orderBy(asc(items.createdAt))
      break
    case 'featured':
      orderedQuery = baseQuery.orderBy(desc(items.featured), desc(items.createdAt))
      break
    default:
      orderedQuery = baseQuery.orderBy(desc(items.createdAt))
  }

  const result = await orderedQuery.limit(limit).offset(offset)
  
  return result
}

// 根据slug获取单个项目详情
export async function getItemBySlug(slug: string) {
  const db = await getDb()
  
  const result = await db
    .select({
      item: items,
      submitter: {
        id: user.id,
        name: user.name,
        image: user.image,
      },
    })
    .from(items)
    .leftJoin(user, eq(items.submitterId, user.id))
    .where(eq(items.slug, slug))

  return result[0] || null
}

// 创建新项目
export async function createItem(data: NewItem) {
  const db = await getDb()
  const result = await db.insert(items).values(data).returning()
  return result[0]
}

// 获取用户提交的项目
export async function getUserItems(userId: string, page = 1, limit = 10) {
  const offset = (page - 1) * limit
  const db = await getDb()
  
  const result = await db
    .select({
      item: items,
    })
    .from(items)
    .where(eq(items.submitterId, userId))
    .orderBy(desc(items.createdAt))
    .limit(limit)
    .offset(offset)
    
  return result
}

// 获取精选项目
export async function getFeaturedItems(limit = 8) {
  return getItems({ 
    featured: true, 
    limit,
    sortBy: 'featured'
  })
}

// 搜索项目建议
export async function searchItemSuggestions(query: string, limit = 5) {
  const db = await getDb()
  
  const suggestions = await db
    .select({
      name: items.name,
      slug: items.slug,
    })
    .from(items)
    .where(
      and(
        like(items.name, `%${query}%`),
        eq(items.freePlanStatus, 'published')
      )
    )
    .orderBy(asc(items.name))
    .limit(limit)

  return suggestions
}
